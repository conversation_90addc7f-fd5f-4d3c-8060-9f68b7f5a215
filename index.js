import 'react-native-reanimated';
/**
 * @format
 */

import { AppRegistry, Platform, Text, TextInput, I18nManager, StyleSheet } from 'react-native';
import App from './App';
import { name as appName } from './app.json';

import 'react-native-get-random-values'

// Vô hiệu hóa font scaling cho toàn bộ ứng dụng
Text.defaultProps = {
    ...(Text.defaultProps || {}),
    allowFontScaling: false,
};

TextInput.defaultProps = {
    ...(TextInput.defaultProps || {}),
    allowFontScaling: false,
};

// // Vô hiệu hóa font scaling cho các component khác có thể bị ảnh hưởng
// if (StyleSheet.setStyleAttributePreprocessor) {
//     const originalFontSizePreprocessor = StyleSheet.setStyleAttributePreprocessor('fontSize', (fontSize) => fontSize);
//     StyleSheet.setStyleAttributePreprocessor('fontSize', (fontSize) => {
//         return originalFontSizePreprocessor(fontSize);
//     });
// }

// Force LTR layout
I18nManager.allowRTL(false);
I18nManager.forceRTL(false);

AppRegistry.registerComponent(appName, () => App);
