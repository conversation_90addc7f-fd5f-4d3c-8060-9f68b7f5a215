# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, '16.0'
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'wini_core_mobile' do
  use_frameworks! :linkage => :static # Change to static linkage
  config = use_native_modules!

  # Configure Google Maps properly
  pod 'GoogleMaps', '7.4.0'  # Uncomment and specify version explicitly
  # pod 'react-native-maps/Google', :path => '../node_modules/react-native-maps' # Comment this line
  pod 'react-native-maps', :path => '../node_modules/react-native-maps' # Use standard maps pod

  # React Native Firebase dependencies
  pod 'RNFBApp', path: '../node_modules/@react-native-firebase/app'
  pod 'RNFBMessaging', path: '../node_modules/@react-native-firebase/messaging'
  pod 'RNFBAuth', path: '../node_modules/@react-native-firebase/auth'
  pod 'react-native-video', :path => '../node_modules/react-native-video'

  # VisionCamera and WebRTC dependencies
  pod 'react-native-webrtc', :path => '../node_modules/react-native-webrtc'
  pod 'VisionCamera', :path => '../node_modules/react-native-vision-camera'

  # Align Firebase versions
  pod 'Firebase/CoreOnly', :modular_headers => true
  pod 'Firebase/Messaging', :modular_headers => true
  pod 'Firebase/Auth', :modular_headers => true

  # Enable modular headers for specific pods
  pod 'FirebaseAuth', :modular_headers => true
  pod 'FirebaseAuthInterop', :modular_headers => true
  pod 'FirebaseAppCheckInterop', :modular_headers => true
  pod 'FirebaseCoreExtension', :modular_headers => true
  pod 'RecaptchaInterop', :modular_headers => true

  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true

  pod 'Firebase/Analytics'

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
    installer.pods_project.targets.each do |target|
      if target.name == 'GoogleMaps'
        target.build_configurations.each do |config|
          config.build_settings['FRAMEWORK_SEARCH_PATHS'] ||= []
          if !config.build_settings['FRAMEWORK_SEARCH_PATHS'].include?('${PODS_ROOT}/GoogleMaps/Base/Frameworks')
            config.build_settings['FRAMEWORK_SEARCH_PATHS'] << '${PODS_ROOT}/GoogleMaps/Base/Frameworks'
          end
          if !config.build_settings['FRAMEWORK_SEARCH_PATHS'].include?('${PODS_ROOT}/GoogleMaps/Maps/Frameworks')
            config.build_settings['FRAMEWORK_SEARCH_PATHS'] << '${PODS_ROOT}/GoogleMaps/Maps/Frameworks'
          end
        end
      end
      target.build_configurations.each do |config|
        config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'

        # Fix for Xcode 15 - see https://github.com/CocoaPods/CocoaPods/issues/12012
        config.build_settings['ENABLE_USER_SCRIPT_SANDBOXING'] = 'NO'

        # Fix for CoreAudioTypes framework not found
        config.build_settings['FRAMEWORK_SEARCH_PATHS'] = ['$(inherited)', '$(SDKROOT)/System/Library/Frameworks']
      end

      # Fix for RCTDeprecation and other targets with libarclite issues
      if target.name == 'RCTDeprecation' || target.name == 'React-Core' || target.name == 'React-RuntimeHermes' || target.name.start_with?('React-')
        target.build_configurations.each do |config|
          config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
        end
      end

      # Fix for react-native-video
      if target.name == 'react-native-video'
        target.build_configurations.each do |config|
          # Add AudioToolbox framework instead of CoreAudioTypes
          config.build_settings['OTHER_LDFLAGS'] ||= ''
          config.build_settings['OTHER_LDFLAGS'] += ' -framework AudioToolbox'

          # Add header search paths for our custom shim
          config.build_settings['HEADER_SEARCH_PATHS'] ||= []
          if !config.build_settings['HEADER_SEARCH_PATHS'].include?('$(SRCROOT)')
            config.build_settings['HEADER_SEARCH_PATHS'] << '$(SRCROOT)'
          end

          # Add framework search paths for system frameworks
          config.build_settings['FRAMEWORK_SEARCH_PATHS'] ||= []
          if !config.build_settings['FRAMEWORK_SEARCH_PATHS'].include?('$(SDKROOT)/System/Library/Frameworks')
            config.build_settings['FRAMEWORK_SEARCH_PATHS'] << '$(SDKROOT)/System/Library/Frameworks'
          end
        end
      end

      # Fix for VisionCamera
      if target.name == 'VisionCamera'
        target.build_configurations.each do |config|
          config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
          config.build_settings['SWIFT_VERSION'] = '5.0'
          config.build_settings['ENABLE_BITCODE'] = 'NO'
        end
      end

      # Fix for react-native-webrtc
      if target.name == 'react-native-webrtc'
        target.build_configurations.each do |config|
          config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
          config.build_settings['ENABLE_BITCODE'] = 'NO'
          config.build_settings['SWIFT_VERSION'] = '5.0'
        end
      end
    end
  end
end
