import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { AppSvg, FDialog, Winicon } from 'wini-mobile-components';
import { navigate, RootScreen } from '../../../router/router';
import CartIcon from '../../../components/CartIcon';
import iconSvg from '../../../svg/icon';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import HeaderBackground from '../../../modules/shop/component/HeaderShop';
import { dialogCheckAcc } from '../mainLayout';
import { useCategoryHook } from '../../../redux/hook/categoryHook';
import { ColorThemes } from '../../../assets/skin/colors';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store/store';

interface HeaderLogoProps {
  isShowBack?: boolean;
}

const HeaderLogo = ({ isShowBack }: HeaderLogoProps) => {
  const { rankInfo } = useSelector((state: RootState) => state.customer);
  const navigation = useNavigation();
  const categoryHook = useCategoryHook();
  const [isVip, setIsVip] = useState(false);

  useEffect(() => {
    if (rankInfo?.achievedRank) {
      setIsVip(Number(rankInfo?.achievedRank?.Sort) > 1);
    }
  }, [rankInfo?.achievedRank]);

  const handleMenuPress = () => {
    categoryHook.setData('showDrawer', true);
  };

  const onBack = () => {
    if (isShowBack) {
      navigation.goBack();
    }
  };

  const getIconMenu = () => {
    if (isVip) {
      return iconSvg.menuUnfoldVip;
    }
    return iconSvg.menuUnfold;
  };

  const customer = useSelectorCustomerState().data;
  const dialogRef = React.useRef<any>(null);

  return (
    <View style={styles.header}>
      <FDialog ref={dialogRef} />
      <View style={styles.headerBackground}>
        <HeaderBackground />
      </View>

      <SafeAreaView style={styles.headerContent}>
        <View style={styles.topRow}>
          {isShowBack ? (
            <TouchableOpacity
              style={styles.backButton}
              activeOpacity={0.7}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              onPress={onBack}>
              <View style={styles.backButtonView}>
                <Winicon
                  src="outline/arrows/left-arrow"
                  size={20}
                  color={ColorThemes.light.neutral_text_title_color}
                />
              </View>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity onPress={handleMenuPress}>
              <AppSvg SvgSrc={getIconMenu()} size={25} />
            </TouchableOpacity>
          )}
          <AppSvg SvgSrc={iconSvg.logoText} size={170} style={styles.logoSvg} />
        </View>
        <View>
          <View style={styles.rightIcons}>
            <Image
              source={require('../../../assets/images/logo.png')}
              style={styles.headerImage}
            />

            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => {
                if (!customer?.Id) {
                  dialogCheckAcc(dialogRef);
                  return;
                }
                navigate(RootScreen.Notification);
              }}>
              <View style={styles.iconCircle}>
                <AppSvg SvgSrc={iconSvg.notification} size={16} />
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => {
                if (!customer?.Id) {
                  dialogCheckAcc(dialogRef);
                  return;
                }
                navigate(RootScreen.CartPage);
              }}>
              <View style={styles.iconCircle}>
                <CartIcon isHome color="#0033CC" size={18} showBadge={true} />
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    width: '100%',
    position: 'relative',
    paddingTop: 16,
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  headerContent: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    zIndex: 2,
    paddingHorizontal: 12,
  },
  topRow: {
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 8,
  },
  iconCircle: {
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  backButton: {
    alignItems: 'center',
    zIndex: 10,
  },
  backButtonView: {
    gap: 4,
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_lighter_background_color,
    borderRadius: 20,
    padding: 6,
  },
  logoSvg: {
    marginLeft: 10,
  },
  headerImage: {
    width: 30,
    height: 30,
    borderRadius: 20,
  },
});

export default HeaderLogo;
