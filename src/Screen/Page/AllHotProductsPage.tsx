import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Text,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ProductDA } from '../../modules/Product/productDA';
import SquareProductCard, { SquareProductItem } from '../../components/SquareProductCard';
import { RootScreen } from '../../router/router';
import { ColorThemes } from '../../assets/skin/colors';import TitleWithBackAction from '../Layout/titleWithBackAction';
;

const { width } = Dimensions.get('window');
const NUM_COLUMNS = 2;
const ITEM_WIDTH = (width - 48) / NUM_COLUMNS;
const ITEM_HEIGHT = ITEM_WIDTH * 1.5;

const AllHotProductsPage = () => {
  const navigation = useNavigation<any>();
  const [products, setProducts] = useState<SquareProductItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 20;

  // Hàm lấy dữ liệu sản phẩm HOT
  const fetchHotProducts = useCallback(async (pageNumber: number, refresh: boolean = false) => {
  const productDA = new ProductDA();

    if (refresh) {
      setLoading(true);
      setProducts([]);
      setPage(1);
      pageNumber = 1;
    } else if (pageNumber > 1) {
      // Đang tải trang tiếp theo
    } else {
      setLoading(true);
    }
    
    setError(null);
    
    try {
      const response = await productDA.getProductHot(pageNumber, pageSize);
      
      if (response && response.code === 200 && response.data) {
        // Chuyển đổi dữ liệu từ API sang định dạng SquareProductItem
        const formattedProducts: SquareProductItem[] = response.data.map((item: any) => ({
          Id: item.Id,
          Name: item.Name,
          Price: item.Price,
          Img: item.Img,
          rating: 4.5, // Giá trị mặc định hoặc từ API nếu có
          soldCount: 200, // Giá trị mặc định hoặc từ API nếu có
          Discount: item.Discount,
        }));
        
        if (refresh || pageNumber === 1) {
          setProducts(formattedProducts);
        } else {
          setProducts(prev => [...prev, ...formattedProducts]);
        }
        
        // Kiểm tra xem còn dữ liệu để tải không
        setHasMore(formattedProducts.length === pageSize);
      } else {
        setError('Không thể tải dữ liệu sản phẩm');
      }
    } catch (err) {
      console.error('Error fetching hot products:', err);
      setError('Đã xảy ra lỗi khi tải dữ liệu');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [pageSize]);

  // Gọi API khi component được mount
  useEffect(() => {
    fetchHotProducts(1);
  }, [fetchHotProducts]);

  // Xử lý khi người dùng kéo xuống để refresh
  const handleRefresh = () => {
    setRefreshing(true);
    fetchHotProducts(1, true);
  };

  // Xử lý khi người dùng kéo đến cuối danh sách để tải thêm
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchHotProducts(nextPage);
    }
  };

  // Xử lý khi nhấn vào sản phẩm
  const handleProductPress = (product: SquareProductItem) => {
    navigation.navigate(RootScreen.ProductDetail, { id: product.Id });
  };

  // Render mỗi item trong grid
  const renderItem = ({ item }: { item: SquareProductItem }) => (
    <View style={styles.itemContainer}>
      <SquareProductCard
        item={item}
        onPress={handleProductPress}
        width={ITEM_WIDTH}
        height={ITEM_HEIGHT}
        showRating={true}
      />
    </View>
  );

  // Render footer khi đang tải thêm dữ liệu
  const renderFooter = () => {
    if (!loading || refreshing) return null;
    
    return (
      <View style={styles.footerContainer}>
        <ActivityIndicator size="small" color={ColorThemes.light.primary_main_color} />
      </View>
    );
  };

  // Render khi không có dữ liệu
  const renderEmpty = () => {
    if (loading && !refreshing) return null;
    
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>
          {error || 'Không có sản phẩm HOT'}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <TitleWithBackAction titleBottom="Sản phẩm HOT" onBack={() => navigation.goBack()} />
      <FlatList
        data={products}
        renderItem={renderItem}
        keyExtractor={(item) => item.Id}
        numColumns={NUM_COLUMNS}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        columnWrapperStyle={styles.columnWrapper}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  listContent: {
    padding: 16,
    paddingBottom: 24,
  },
  columnWrapper: {
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  itemContainer: {
    marginBottom: 8,
  },
  footerContainer: {
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontSize: 16,
  },
});

export default AllHotProductsPage;
