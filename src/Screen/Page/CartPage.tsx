import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  StatusBar,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useCartState, useCartActions} from '../../redux/hook/cartHook';
import {CartItem} from '../../redux/types/cartTypes';
import {
  Winicon,
  ComponentStatus,
  showSnackbar,
  Checkbox,
  FDialog,
  showDialog,
} from 'wini-mobile-components';
import {TypoSkin} from '../../assets/skin/typography';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../Config/ConfigAPI';
import {CustomerDA} from '../../modules/customer/da';
import {Ultis} from '../../utils/Utils';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import EmptyPage from '../emptyPage';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import {useDispatch} from 'react-redux';
import {RootScreen} from '../../router/router';
import {InforHeader} from '../Layout/headers/inforHeader';
import {dialogCheckAcc} from '../Layout/mainLayout';
import HotProductsSection from '../../modules/Product/HotProductsSection';

const {width} = Dimensions.get('window');
const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;

const CartPage: React.FC = () => {
  const navigation = useNavigation<any>();
  const cartState = useCartState();
  const cartActions = useCartActions();
  const customerDA = new CustomerDA();
  const customer = useSelectorCustomerState().data;
  const dispatch = useDispatch<any>();
  const customerAdress = useSelectorCustomerState().myAddress;
  const address = customerAdress?.find(item => item.IsDefault);
  const dialogRef = React.useRef<any>(null);

  React.useEffect(() => {
    console.log('check-cartState', cartState);
  }, []);

  React.useEffect(() => {
    if (customer) dispatch(CustomerActions.getAddresses(customer.Id));
  }, []);

  // Nhóm các sản phẩm theo cửa hàng
  const storeGroups = cartActions.groupItemsByStore(cartState.items);

  // Tính tổng tiền của các sản phẩm đã chọn
  const totalSelectedPrice = cartActions.calculateSelectedTotal(
    cartState.items,
  );

  // Kiểm tra xem có sản phẩm nào được chọn không
  const hasSelectedItems = cartActions.hasSelectedItems(cartState.items);

  const deleteProd = (id: string) => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.SUCCSESS,
      title: 'Bạn chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?',
      onSubmit: async () => {
        cartActions.removeItem(id);
        showSnackbar({
          message: 'Xóa sản phẩm thành công',
          status: ComponentStatus.SUCCSESS,
        });
      },
    });
  };

  // Xử lý khi nhấn nút thanh toán
  const handleCheckout = () => {
    if (!customer) {
      dialogCheckAcc(dialogRef);
      showSnackbar({
        message: 'Vui lòng đăng nhập để tiếp tục thanh toán',
        status: ComponentStatus.WARNING,
      });
      return;
    }

    if (!address) {
      showSnackbar({
        message: 'Vui lòng cập nhật điểm giao hàng để tiếp tục thanh toán',
        status: ComponentStatus.WARNING,
      });
      return;
    }

    if (!hasSelectedItems) {
      showSnackbar({
        message: 'Vui lòng chọn ít nhất một sản phẩm để thanh toán',
        status: ComponentStatus.WARNING,
      });
      return;
    }

    // Lọc các sản phẩm đã chọn
    const selectedItems = cartState.items.filter(item => item.selected);

    // Chuyển đến trang thanh toán với các sản phẩm đã chọn
    navigation.navigate(RootScreen.CheckoutPage, {
      items: selectedItems,
      address: address,
    });
  };

  // Render một sản phẩm trong giỏ hàng
  const renderCartItem = (item: CartItem) => {
    return (
      <View key={item.id} style={styles.cartItemContainer}>
        <View style={styles.checkboxContainer}>
          <Checkbox
            value={item.selected}
            onChange={value => {
              cartActions.toggleItemSelection(item.id);
            }}
          />
        </View>

        <View style={styles.itemImageContainer}>
          <FastImage
            style={styles.itemImage}
            source={{
              uri: item.Img?.startsWith('http')
                ? item.Img
                : `${ConfigAPI.urlImg}${item.Img}`,
              priority: FastImage.priority.normal,
            }}
            resizeMode={FastImage.resizeMode.cover}
          />
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => deleteProd(item.id)}>
            <Winicon src="outline/layout/trash" size={20} color="red" />
          </TouchableOpacity>
        </View>

        <View style={styles.itemDetails}>
          <TouchableOpacity
            style={{flex: 1}}
            onPress={() =>
              navigation.push(RootScreen.ProductDetail, {id: item.ProductId})
            }>
            <Text style={styles.itemDescription} numberOfLines={2}>
              {item.Name}
            </Text>

            <View style={styles.priceContainer}>
              {item.Discount && item.Discount > 0 ? (
                <View
                  style={{flexDirection: 'row', gap: 8, alignItems: 'center'}}>
                  <Text style={styles.originalPrice}>
                    {Ultis.money(item.Price ?? 0)} đ
                  </Text>
                  <Text style={styles.currentPrice}>
                    {Ultis.money(
                      item.Price - (item.Price * item.Discount) / 100,
                    )}{' '}
                    đ
                  </Text>
                </View>
              ) : (
                <Text style={styles.currentPrice}>
                  {Ultis.money(item.Price ?? 0)} đ
                </Text>
              )}
            </View>
          </TouchableOpacity>
          <View style={styles.quantityContainer}>
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={async () => {
                if (item.Quantity == 1) {
                  deleteProd(item.id);
                  return;
                }
                if (item.Quantity > 1) {
                  await cartActions.updateItemQuantity(
                    item.id,
                    item.Quantity - 1,
                  );
                }
              }}>
              <Winicon src="outline/layout/minus" size={16} color="#2962FF" />
            </TouchableOpacity>

            <Text style={styles.quantityText}>{item.Quantity}</Text>

            <TouchableOpacity
              style={styles.quantityButton}
              onPress={async () =>
                await cartActions.updateItemQuantity(item.id, item.Quantity + 1)
              }>
              <Winicon src="outline/layout/plus" size={16} color="#2962FF" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  // Render một nhóm cửa hàng
  const renderStoreGroup = (storeGroup: any) => {
    const allSelected = storeGroup?.items?.every((item: any) => item.selected);
    return (
      <View key={storeGroup.ShopId} style={styles.storeContainer}>
        <View style={styles.storeHeader}>
          <View style={styles.storeInfo}>
            <FastImage
              style={styles.storeAvatar}
              source={{
                uri: storeGroup.ShopAvatar?.startsWith('http')
                  ? storeGroup.ShopAvatar
                  : `${ConfigAPI.urlImg}${storeGroup.ShopAvatar}`,
                priority: FastImage.priority.normal,
              }}
              resizeMode={FastImage.resizeMode.cover}
            />

            <Text style={styles.storeName}>{storeGroup.ShopName}</Text>
          </View>
          {!storeGroup.ShopId ? null : (
            <View style={styles.storeCheckbox}>
              <Checkbox
                value={allSelected}
                onChange={value => {
                  cartActions.toggleStoreSelection(storeGroup.ShopId, value);
                }}
              />
            </View>
          )}
        </View>

        {storeGroup.items.map((item: any) => renderCartItem(item))}
      </View>
    );
  };

  // Render bottom bar với nút thanh toán
  const renderBottomBar = () => {
    return (
      <View style={styles.bottomBar}>
        <View style={styles.totalContainer}>
          <Text style={styles.totalLabel}>Tổng cộng:</Text>
          <Text style={styles.totalPrice}>
            {Ultis.money(totalSelectedPrice ?? 0)} đ
          </Text>
        </View>

        <TouchableOpacity
          style={[
            styles.checkoutButton,
            !hasSelectedItems ? styles.checkoutButtonDisabled : {},
          ]}
          onPress={handleCheckout}
          disabled={!hasSelectedItems}>
          <Text style={styles.checkoutButtonText}>Thanh toán</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <FDialog ref={dialogRef} />
      <RenderHeaderCart title="Giỏ hàng" />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}>
        <RenderRecipientInfo dialogRef={dialogRef} />

        {storeGroups.length > 0 ? (
          storeGroups.map((storeGroup: any) => renderStoreGroup(storeGroup))
        ) : (
          <EmptyPage title="Giỏ hàng của bạn trống" />
        )}

        <View style={styles.bottomSpacer} />
        <HotProductsSection
          title="Gợi ý cho bạn"
          pageSize={10}
          onSeeAll={() => navigation.navigate(RootScreen.ProductListByCategory)}
        />
      </ScrollView>

      {renderBottomBar()}
    </SafeAreaView>
  );
};

// Render thông tin người nhận
export const RenderRecipientInfo = ({dialogRef, isDone}: any) => {
  const navigation = useNavigation<any>();
  const customerAdress = useSelectorCustomerState().myAddress;
  const customer = useSelectorCustomerState().data;
  const address = customerAdress?.find(item => item.IsDefault);
  return (
    <View style={styles.recipientInfoContainer}>
      <View style={styles.recipientHeader}>
        <Text style={styles.recipientTitle}>Thông tin Người nhận</Text>
        {isDone ? null : (
          <TouchableOpacity
            onPress={() => {
              if (!customer) {
                dialogCheckAcc(dialogRef);
                showSnackbar({
                  message: 'Vui lòng đăng nhập để tiếp tục thanh toán',
                  status: ComponentStatus.WARNING,
                });
                return;
              }
              navigation.navigate(RootScreen.MyAddress, {chooseAddress: true});
            }}
            style={styles.editButton}>
            <Winicon
              src="outline/user interface/edit"
              size={20}
              color="#2962FF"
            />
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.recipientDetails}>
        <Text style={styles.recipientName}>
          {address
            ? `${address?.Name} • ${address?.Mobile} • ${address?.Email ?? ''}`
            : `Chưa có điểm giao hàng`}
        </Text>
        <Text style={styles.recipientName}>
          {address ? `${address?.Address ?? ''}` : ``}
        </Text>
      </View>
    </View>
  );
};

export const RenderHeaderCart = ({title = 'Giỏ hàng'}: {title?: string}) => {
  const navigation = useNavigation<any>();
  return <InforHeader title={title} onBack={() => navigation.goBack()} />;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    width: '100%',
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  waveContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 2,
    overflow: 'hidden',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingHorizontal: 16,
  },
  recipientInfoContainer: {
    backgroundColor: '#E6F7FF',
    borderRadius: 10,
    padding: 16,
    marginTop: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#ccc',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  recipientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  recipientTitle: {
    ...TypoSkin.title5,
    color: '#000000',
    fontWeight: '700',
  },
  editButton: {
    padding: 4,
  },
  recipientDetails: {
    gap: 4,
  },
  recipientName: {
    color: '#000000',
    lineHeight: 16,
    fontSize: 12,
    fontWeight: '400',
  },
  recipientAddress: {
    ...TypoSkin.body3,
    color: '#666666',
  },
  storeContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    marginVertical: 8,
  },
  storeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  storeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  storeCheckbox: {
    marginLeft: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    padding: 2,
    borderColor: '#E8E8E8',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  checkboxSelected: {
    backgroundColor: '#2962FF',
  },
  storeAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  storeName: {
    fontSize: 18,
    color: '#000000',
    fontWeight: '700',
  },
  cartItemContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  checkboxContainer: {
    marginRight: 12,
    alignSelf: 'center',
  },
  itemImageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  itemImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
    backgroundColor: '#fff',
    //shadow
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  deleteButton: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    backgroundColor: '#fff',
    width: 32,
    height: 32,
    borderRadius: 12,
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  itemDetails: {
    flex: 1,
    justifyContent: 'space-between',
  },
  itemDescription: {
    ...TypoSkin.title3,
    marginBottom: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  currentPrice: {
    ...TypoSkin.title3,
    color: '#FF3B30',
    marginRight: 8,
    fontWeight: '700',
  },
  originalPrice: {
    ...TypoSkin.title5,
    color: '#000',
    textDecorationLine: 'line-through',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  quantityButton: {
    width: 23,
    height: 23,
    borderRadius: 14,
    borderColor: '#1C33FF',
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    ...TypoSkin.body2,
    marginHorizontal: 12,
    minWidth: 20,
    textAlign: 'center',
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  totalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalLabel: {
    ...TypoSkin.body2,
    color: '#000000',
    marginRight: 8,
    fontWeight: '700',
  },
  totalPrice: {
    ...TypoSkin.heading6,
    color: '#FF3B30',
    fontWeight: '700',
  },
  checkoutButton: {
    backgroundColor: '#2962FF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  checkoutButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  checkoutButtonText: {
    ...TypoSkin.heading7,
    color: '#FFFFFF',
  },
  bottomSpacer: {
    height: 100,
  },
});

export default CartPage;
