import React, {useState, useRef} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import PagerView from 'react-native-pager-view';
import {useNavigation} from '@react-navigation/native';
import {showSnackbar, ComponentStatus, FLoading} from 'wini-mobile-components';
import ScreenNewHeader from '../../Layout/header';
import StepIndicator from '../../../components/StepIndicator';
import Step1Introduction from './Step1Introduction';
import Step2PasswordVerification from './Step2PasswordVerification';
import Step3QRCodeSetup from './Step3QRCodeSetup';
import Step4VerificationCode from './Step4VerificationCode';
import {ColorThemes} from '../../../assets/skin/colors';
import {InforHeader} from '../../Layout/headers/inforHeader';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {AppDispatch} from '../../../redux/store/store';
import {useDispatch} from 'react-redux';

const {width} = Dimensions.get('window');
const TwoFactorAuthScreen = () => {
  const navigation = useNavigation();
  const pagerRef = useRef<PagerView>(null);
  const customer = useSelectorCustomerState().data;
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [password, setPassword] = useState('');
  const [qrCodeValue, setQRCodeValue] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [secretKey, setSecretKey] = useState('');
  const totalSteps = 4;
  //dispatch
  const dispatch = useDispatch<AppDispatch>();

  const goToNextStep = () => {
    if (currentStep < totalSteps - 1) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      pagerRef.current?.setPage(nextStep);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      pagerRef.current?.setPage(prevStep);
    }
  };

  const handleStep1Next = async () => {
    if (customer?.IsEnable2FA) {
      setCurrentStep(currentStep + 3);
      pagerRef.current?.setPage(currentStep + 3);
    } else {
      goToNextStep();
    }
  };

  const handleStep2Next = async (enteredPassword: string) => {
    if (!enteredPassword.trim()) {
      showSnackbar({
        message: 'Vui lòng nhập mật khẩu',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    setIsLoading(true);
    try {
      // TODO: Verify password with API
      setPassword(enteredPassword);
      // check password
      const res = await CustomerActions.checkPassword(
        customer?.Mobile || '',
        enteredPassword,
      );
      if (res.code !== 200) {
        showSnackbar({
          message: 'Mật khẩu không chính xác',
          status: ComponentStatus.ERROR,
        });
        return;
      }
      //verify 2fa
      const resVerify = await CustomerActions.setUp2FA(customer.Id);
      if (resVerify.code === 200) {
        setSecretKey(resVerify.data.secret);
        setQRCodeValue(resVerify.data.qrCode);
        goToNextStep();
      } else {
        showSnackbar({
          message: 'Đã có lỗi xảy ra',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      showSnackbar({
        message: 'Mật khẩu không chính xác',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStep3Next = () => {
    goToNextStep();
  };

  const handleStep4Complete = async (code: string) => {
    if (code.length !== 6) {
      showSnackbar({
        message: 'Vui lòng nhập đầy đủ mã xác thực',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Verify OTP code with API
      // For now, simulate API call
      if (customer?.IsEnable2FA) {
        const result = await CustomerActions.verify2Action(customer.Id, code);
        if (result.code !== 200) {
          showSnackbar({
            message: 'Mã xác thực không chính xác',
            status: ComponentStatus.ERROR,
          });
          return;
        }
        const resVerify = await CustomerActions.disable2FA(customer.Id);
        if (resVerify.code !== 200) {
          showSnackbar({
            message: 'Vui lòng thử lại',
            status: ComponentStatus.ERROR,
          });
          return;
        }
        showSnackbar({
          message: 'Tắt xác thực 2 lớp thành công!',
          status: ComponentStatus.SUCCSESS,
        });
        dispatch(CustomerActions.getInfor());
        // Navigate back or to success screen
        setCurrentStep(0);
        pagerRef.current?.setPage(0);
      } else {
        const resVerify = await CustomerActions.verify2FA(customer.Id, code);
        if (resVerify.code !== 200) {
          showSnackbar({
            message: 'Mã xác thực không chính xác',
            status: ComponentStatus.ERROR,
          });
          return;
        }
        showSnackbar({
          message: 'Bật xác thực 2 lớp thành công!',
          status: ComponentStatus.SUCCSESS,
        });
        dispatch(CustomerActions.getInfor());
        // Navigate back or to success screen
        setCurrentStep(0);
        pagerRef.current?.setPage(0);
      }
    } catch (error) {
      showSnackbar({
        message: 'Mã xác thực không chính xác',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getHeaderTitle = () => {
    switch (currentStep) {
      case 0:
        return 'QL 2 MFA';
      case 1:
      case 2:
      case 3:
        return 'Xác thực Authenticator';
      default:
        return 'QL 2 MFA';
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      goToPreviousStep();
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={styles.container}>
      <FLoading visible={isLoading} />

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.innerContainer}>
            <InforHeader title={'QL 2 MFA'} />

            {/* Step Indicator - Only show for steps 1, 2, 3 */}
            {currentStep > 0 && (
              <View style={styles.stepIndicatorContainer}>
                <StepIndicator currentStep={currentStep} totalSteps={3} />
              </View>
            )}

            <PagerView
              ref={pagerRef}
              style={styles.pagerView}
              initialPage={0}
              scrollEnabled={false}>
              <View key="step1" style={styles.pageContainer}>
                <Step1Introduction onNext={handleStep1Next} />
              </View>

              <View key="step2" style={styles.pageContainer}>
                <Step2PasswordVerification
                  onNext={handleStep2Next}
                  isLoading={isLoading}
                />
              </View>

              <View key="step3" style={styles.pageContainer}>
                <Step3QRCodeSetup
                  secretKey={secretKey}
                  qrCodeValue={qrCodeValue || 'none'}
                  onNext={handleStep3Next}
                />
              </View>

              <View key="step4" style={styles.pageContainer}>
                <Step4VerificationCode
                  onComplete={handleStep4Complete}
                  isLoading={isLoading}
                />
              </View>
            </PagerView>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  innerContainer: {
    flex: 1,
  },
  stepIndicatorContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  pagerView: {
    flex: 1,
  },
  pageContainer: {
    flex: 1,
    paddingHorizontal: 10,
  },
});

export default TwoFactorAuthScreen;
