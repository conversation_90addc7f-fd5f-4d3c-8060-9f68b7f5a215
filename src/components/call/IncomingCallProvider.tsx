import React, { useEffect, useState } from 'react';
import FloatingIncoming<PERSON>all from './FloatingIncomingCall';
import IncomingCallOverlayService from '../../services/IncomingCallOverlayService';
import WebRTCService from '../../features/call/WebRTCService';
import { navigate, RootScreen } from '../../router/router';

interface CallData {
  callerName: string;
  callerAvatar?: string;
  callerId: string;
}

const IncomingCallProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [showNotification, setShowNotification] = useState(false);
  const [callData, setCallData] = useState<CallData | null>(null);
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Define functions outside useEffect để có thể access từ handlers
  const handleHideIncomingCall = React.useCallback(() => {
    console.log('📞 handleHideIncomingCall called, current state:', { showNotification, callData });

    // Clear timeout khi ẩn notification
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
      console.log('📞 Timeout cleared');
    }

    setShowNotification(false);
    setCallData(null);
    console.log('📞 Notification hidden, state reset');
  }, [showNotification, callData]);

  const handleAcceptCall = React.useCallback(async (data: CallData) => {
    try {
      console.log('📞 Accepting call from:', data.callerName);

      // Accept call through WebRTC
      await WebRTCService.acceptCall();

      // Ẩn notification
      handleHideIncomingCall();

      // Navigate to CallScreen
      navigate(RootScreen.CallScreen, {
        isIncoming: false,
        callerId: data.callerId,
        callerName: data.callerName,
        callerAvatar: data.callerAvatar,
      });
    } catch (error) {
      console.error('Error accepting call:', error);
    }
  }, [handleHideIncomingCall]);

  const handleRejectCall = React.useCallback((data: CallData) => {
    console.log('📞 Rejecting call from:', data.callerName);

    // Ẩn notification trước
    handleHideIncomingCall();

    // Reject call through WebRTC
    WebRTCService.rejectCall();
  }, [handleHideIncomingCall]);

  useEffect(() => {
    console.log('📞 IncomingCallProvider useEffect mounting, current state:', { showNotification, callData });

    // Listen for incoming call events
    const handleShowIncomingCall = (data: CallData) => {
      console.log('📞 handleShowIncomingCall received data:', data);
      console.log('📞 Current notification state before show:', { showNotification, callData });

      // Force reset state trước khi show notification mới
      setShowNotification(false);
      setCallData(null);

      // Delay một chút để đảm bảo state được reset
      setTimeout(() => {
        setCallData(data);
        setShowNotification(true);
        console.log('📞 Notification shown with new data');
      }, 100);

      // Set timeout để tự động ẩn notification sau 60 giây
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        console.log('📞 Notification timeout, hiding notification');
        handleHideIncomingCall();
      }, 60000); // 60 giây
    };

    // Listen for WebRTC call state changes để auto-hide notification
    const handleCallStateChanged = (state: any) => {
      // Nếu cuộc gọi kết thúc (không còn trong call), ẩn notification
      if (!state.isInCall && showNotification) {
        console.log('📞 Call ended, hiding notification');
        handleHideIncomingCall();
      }
    };

    // Setup WebRTC callback để listen call state changes
    WebRTCService.setCallbacks({
      onCallStateChanged: handleCallStateChanged,
      onCallEnded: () => {
        console.log('📞 WebRTC call ended, hiding notification');
        handleHideIncomingCall();
      },
      onCallRejected: () => {
        console.log('📞 Call rejected, hiding notification');
        handleHideIncomingCall();
      },
      onError: (error) => {
        console.log('📞 WebRTC error, hiding notification:', error);
        handleHideIncomingCall();
      },
    });

    // Clear existing listeners trước khi add mới (tránh duplicate)
    console.log('📞 Clearing existing listeners');
    IncomingCallOverlayService.off('showIncomingCall', handleShowIncomingCall);
    IncomingCallOverlayService.off('hideIncomingCall', handleHideIncomingCall);
    IncomingCallOverlayService.off('acceptCall', handleAcceptCall);
    IncomingCallOverlayService.off('rejectCall', handleRejectCall);

    // Register event listeners
    console.log('📞 Registering event listeners');
    IncomingCallOverlayService.on('showIncomingCall', handleShowIncomingCall);
    IncomingCallOverlayService.on('hideIncomingCall', handleHideIncomingCall);
    IncomingCallOverlayService.on('acceptCall', handleAcceptCall);
    IncomingCallOverlayService.on('rejectCall', handleRejectCall);
    console.log('📞 Event listeners registered, total listeners:', IncomingCallOverlayService.listenerCount('showIncomingCall'));

    return () => {
      // Cleanup timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Cleanup event listeners
      IncomingCallOverlayService.off('showIncomingCall', handleShowIncomingCall);
      IncomingCallOverlayService.off('hideIncomingCall', handleHideIncomingCall);
      IncomingCallOverlayService.off('acceptCall', handleAcceptCall);
      IncomingCallOverlayService.off('rejectCall', handleRejectCall);
    };
  }, [handleHideIncomingCall, handleAcceptCall, handleRejectCall, showNotification]);

  const handleAccept = () => {
    console.log('📞 User tapped Accept button, callData:', callData);
    if (callData) {
      // Clear timeout khi accept
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      debugger
      // Gọi handleAcceptCall với data
      handleAcceptCall(callData);
    } else {
      console.error('📞 No callData available for accept');
    }
  };

  const handleReject = () => {
    console.log('📞 User tapped Reject button, callData:', callData);
    if (callData) {
      // Clear timeout khi reject
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      // Gọi handleRejectCall với data
      handleRejectCall(callData);
    } else {
      console.error('📞 No callData available for reject');
    }
  };

  const handleMinimize = () => {
    console.log('📞 User minimized notification');
    // Clear timeout khi minimize
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // Ẩn notification nhưng không reject call
    setShowNotification(false);
    // Call vẫn đang ring, có thể show mini button hoặc để user tự quay lại
  };

  // Debug effect để track state changes
  React.useEffect(() => {
    console.log('📞 IncomingCallProvider state changed:', {
      showNotification,
      callerName: callData?.callerName,
      callerId: callData?.callerId
    });
  }, [showNotification, callData]);

  return (
    <>
      {children}

      {/* Floating Incoming Call - không block tương tác */}
      <FloatingIncomingCall
        visible={showNotification}
        callerName={callData?.callerName || 'Người dùng'}
        callerAvatar={callData?.callerAvatar}
        onAccept={handleAccept}
        onReject={handleReject}
        onMinimize={handleMinimize}
      />
    </>
  );
};

export default IncomingCallProvider;
