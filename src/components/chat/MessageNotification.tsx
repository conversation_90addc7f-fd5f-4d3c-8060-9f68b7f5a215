import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  StatusBar,
  Platform,
} from 'react-native';
import { ColorThemes } from '../../assets/skin/colors';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../Config/ConfigAPI';
import { Winicon } from 'wini-mobile-components';
import { ChatMessage, ChatRoom } from '../../modules/chat/types/ChatTypes';

interface MessageNotificationProps {
  visible: boolean;
  message: ChatMessage;
  room: ChatRoom | null;
  senderName: string;
  messagePreview: string;
  onTap: () => void;
  onDismiss: () => void;
}

const MessageNotification: React.FC<MessageNotificationProps> = ({
  visible,
  message,
  room,
  senderName,
  messagePreview,
  onTap,
  onDismiss,
}) => {
  const slideAnim = useRef(new Animated.Value(-200)).current;
  const overlayOpacity = useRef(new Animated.Value(0)).current;
  //random color 
  const getRandomAvatarColor = (name: string) => {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  useEffect(() => {
    if (visible) {
      // Fade in overlay
      Animated.timing(overlayOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Slide down notification
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      // Fade out overlay
      Animated.timing(overlayOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Slide up notification
      Animated.timing(slideAnim, {
        toValue: -200,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  if (!visible) return null;

  const senderAvatar = message.user?.Avatar || room?.Avatar;

  return (
    <Animated.View style={[styles.overlay, { opacity: overlayOpacity }]}>
      <StatusBar barStyle="light-content" backgroundColor="rgba(0,0,0,0.5)" />

      {/* Touch area để dismiss - chỉ phần trên notification */}
      <TouchableOpacity
        style={styles.dismissArea}
        activeOpacity={1}
        onPress={() => {
          console.log('💬 User tapped outside notification');
          onDismiss();
        }}
      />

      <Animated.View style={[styles.container, { transform: [{ translateY: slideAnim }] }]}>
        <TouchableOpacity style={styles.card} onPress={onTap} activeOpacity={0.9}>
          <View style={styles.avatarContainer}>
            {senderAvatar ? (
              <FastImage
                source={{ uri: ConfigAPI.urlImg + senderAvatar }}
                style={styles.avatar}
              />
            ) : (
              <View style={[styles.defaultAvatar, {backgroundColor: getRandomAvatarColor(senderName)}]}>
                <Text style={styles.avatarText}>
                  {senderName?.charAt(0).toUpperCase() || '?'}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.infoContainer}>
            <Text style={styles.name} numberOfLines={1}>{senderName}</Text>
            <Text style={styles.message} numberOfLines={2}>{messagePreview}</Text>
          </View>

          <View style={styles.actions}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={(e) => {
                e.stopPropagation();
                onDismiss();
              }}
              activeOpacity={0.8}
            >
              <Winicon src="outline/layout/xmark" size={16} color="#666" />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Animated.View>

      {/* Bottom area - cho phép tương tác với app bên dưới */}
      <View style={styles.bottomArea} pointerEvents="none" />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 70, // Chỉ cover phần top thay vì toàn màn hình
    backgroundColor: 'rgba(0, 0, 0, 0.2)', // Nhẹ hơn call notification
    zIndex: 9999, // Thấp hơn call notification (10000)
    justifyContent: 'flex-start',
  },
  container: {
    marginTop: Platform.OS === 'ios' ? 50 : (StatusBar.currentHeight || 0) + 10,
    marginHorizontal: 8,
    backgroundColor: 'transparent',
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOpacity: 0.15,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 6,
    elevation: 6,
    minHeight: 70,
  },
  avatarContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: 'hidden',
    marginRight: 12,
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  avatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 4,
  },
  name: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  message: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  actions: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 8,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  dismissArea: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: Platform.OS === 'ios' ? 50 : (StatusBar.currentHeight || 0) + 10,
    backgroundColor: 'transparent',
  },
  bottomArea: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '70%', // 70% bottom của màn hình cho phép tương tác
    backgroundColor: 'transparent',
  },
});

export default MessageNotification;
