import React from 'react';
import { View, StyleSheet } from 'react-native';
import InforShopView from './inforShopView';

// Mock shop data để test
const mockShop = {
  Id: '1',
  Name: '<PERSON><PERSON><PERSON><PERSON>',
  Avatar: null, // Để test trường hợp không có avatar
  Mobile: '0912345135',
  Email: 'tungnt@innotechjsc@.com',
  ContactName: '<PERSON><PERSON><PERSON><PERSON>',
  ManagerName: 'Nguyễn Trọng Tú',
  OwnerName: 'Nguyễn Trung Kiên',
  rating: 4.5,
  totalProducts: 90,
  totalOrder: 3000,
  Status: 1,
};

const TestInforShopView = () => {
  // Mock route params
  const mockRoute = {
    params: {
      shop: mockShop
    }
  };

  // Mock useRoute hook
  React.useEffect(() => {
    // Override useRoute for testing
    const originalModule = require('@react-navigation/native');
    originalModule.useRoute = () => mockRoute;
  }, []);

  return (
    <View style={styles.container}>
      <InforShopView />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default TestInforShopView;
