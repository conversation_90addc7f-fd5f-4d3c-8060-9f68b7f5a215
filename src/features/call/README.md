# Audio Call Feature - WebRTC Implementation

## Tổng quan
Chức năng cuộc gọi âm thanh sử dụng WebRTC đã được tích hợp vào ứng dụng Chainivo. Hệ thống hỗ trợ cuộc gọi 1-1 gi<PERSON>a các người dùng thông qua socket connection.

## Cấu trúc Files

### Core Services
- `src/features/call/WebRTCService.tsx` - Service chính xử lý WebRTC
- `src/services/CallNotificationService.ts` - Xử lý thông báo cuộc gọi đến
- `src/modules/call/screens/CallScreen.tsx` - UI màn hình cuộc gọi

### Integration Points
- `src/modules/chat/screens/ContactsScreen.tsx` - Tích hợp nút gọi
- `src/utils/SocketConnectionManager.ts` - Khởi tạo call service
- `src/modules/chat/services/SocketService.ts` - Thêm getSocket() method

## Socket Events (Server Side)

Các events sau đây đã được implement trên server và được sử dụng:

```javascript
// Bắt đầu cuộc gọi
socket.emit('call-user', { targetUserId: string })
socket.on('incoming-call', { from: string, socketId: string })

// Chấp nhận/từ chối cuộc gọi
socket.emit('accept-call', { from: string })
socket.on('accept-call', { from: string })

socket.emit('reject-call', { from: string })
socket.on('reject-call', { from: string })

// WebRTC signaling
socket.emit('offer', { targetUserId: string, offer: RTCSessionDescription })
socket.on('offer', { offer: RTCSessionDescription, from: string })

socket.emit('answer', { targetUserId: string, answer: RTCSessionDescription })
socket.on('answer', { answer: RTCSessionDescription, from: string })

socket.emit('candidate', { candidate: RTCIceCandidate, targetUserId: string })
socket.on('candidate', candidate: RTCIceCandidate)

// Kết thúc cuộc gọi
socket.emit('end-call', { targetUserId: string })
socket.on('end-call', { from: string })
```

## Cách sử dụng

### 1. Thực hiện cuộc gọi từ ContactsScreen
- Người dùng nhấn nút call (📞) bên cạnh contact
- Hệ thống hiển thị confirm dialog
- Khi confirm, `WebRTCService.startCall()` được gọi
- Tự động navigate đến `CallScreen`

### 2. Nhận cuộc gọi đến
- Server gửi event `incoming-call`
- `CallNotificationService` hiển thị Alert dialog
- Người dùng có thể chấp nhận hoặc từ chối
- Nếu chấp nhận, navigate đến `CallScreen` và gọi `WebRTCService.acceptCall()`

### 3. Trong cuộc gọi
- CallScreen hiển thị thông tin người gọi
- Các nút điều khiển: mute, speaker, end call
- Timer hiển thị thời gian cuộc gọi
- Tự động quay về màn hình trước khi cuộc gọi kết thúc

## API Reference

### WebRTCService

```typescript
// Bắt đầu cuộc gọi
await WebRTCService.startCall(targetUserId: string, targetUserName?: string)

// Chấp nhận cuộc gọi đến
await WebRTCService.acceptCall()

// Từ chối cuộc gọi
WebRTCService.rejectCall()

// Kết thúc cuộc gọi
WebRTCService.endCall()

// Kiểm tra trạng thái
WebRTCService.isInCall(): boolean
WebRTCService.getCallState(): CallState

// Thiết lập callbacks
WebRTCService.setCallbacks(callbacks: WebRTCCallbacks)
```

### CallState Interface

```typescript
interface CallState {
  isInCall: boolean;
  isIncoming: boolean;
  isOutgoing: boolean;
  isConnected: boolean;
  targetUserId: string | null;
  targetUserName: string | null;
  callStartTime: Date | null;
}
```

## Navigation

CallScreen được thêm vào routing với tên `RootScreen.CallScreen`:

```typescript
navigate(RootScreen.CallScreen, {
  contact?: { Id: string, Name: string, AvatarUrl?: string },
  isIncoming?: boolean,
  callerId?: string,
  callerName?: string
})
```

## Lưu ý kỹ thuật

1. **Socket Connection**: WebRTC sử dụng chung socket connection với chat system
2. **Audio Only**: Hiện tại chỉ hỗ trợ cuộc gọi âm thanh (audio: true, video: false)
3. **ICE Servers**: Sử dụng Google STUN servers, có thể cần TURN servers cho production
4. **Error Handling**: Tất cả lỗi được xử lý và hiển thị snackbar thông báo
5. **Cleanup**: Tự động dọn dẹp resources khi cuộc gọi kết thúc

## Testing

Để test chức năng:
1. Đảm bảo 2 device/emulator đã đăng nhập với user khác nhau
2. Đảm bảo socket connection hoạt động
3. Từ ContactsScreen, nhấn nút call để thực hiện cuộc gọi
4. Kiểm tra incoming call notification trên device khác
5. Test các chức năng accept/reject/end call

## Troubleshooting

### Common Issues Fixed:

1. **`addStream is not a function`**:
   - ✅ Fixed: Sử dụng `addTrack()` thay vì `addStream()` cho react-native-webrtc mới
   - ✅ Fixed: Sử dụng `ontrack` thay vì `onaddstream` để nhận remote stream

2. **Permission Issues**:
   - Đảm bảo có microphone permissions trong AndroidManifest.xml:
   ```xml
   <uses-permission android:name="android.permission.RECORD_AUDIO" />
   ```
   - iOS: Thêm vào Info.plist:
   ```xml
   <key>NSMicrophoneUsageDescription</key>
   <string>This app needs microphone access for audio calls</string>
   ```

### Other Issues:

- **Socket not connected**: Kiểm tra SocketConnectionManager và ConfigAPI.Socketurl
- **No audio**: Kiểm tra permissions microphone trong AndroidManifest.xml/Info.plist
- **Connection failed**: Kiểm tra ICE servers và network connectivity
- **Navigation issues**: Đảm bảo CallScreen đã được thêm vào navigator

### Testing:

Sử dụng `WebRTCTest` component để test functionality:
```typescript
import WebRTCTest from '../features/call/WebRTCTest';
// Render WebRTCTest component để kiểm tra permissions và call functionality
```
