import React, {useEffect, useRef, useState} from 'react';
import MapView, {PROVIDER_GOOGLE} from 'react-native-maps';
import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Geolocation from '@react-native-community/geolocation';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
  faAngleLeft,
  faLocation,
  faLocationArrow,
} from '@fortawesome/free-solid-svg-icons';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import {SvgXml} from 'react-native-svg';
import {Ultis} from '../../../utils/Utils';

export const OutlineLocation = ({
  size,
  color,
  opacity = 1,
}: {
  size?: number;
  color?: string;
  opacity?: number;
}) => (
  <SvgXml
    xml={`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.88341 2.49292C5.88426 2.49292 3.82544 3.97 3.82544 6.55089C3.82544 7.1218 4.06473 7.84829 4.48515 8.66023C4.89906 9.45959 5.45758 10.2837 6.02649 11.0339C6.59387 11.7822 7.16265 12.4453 7.59027 12.9219C7.69787 13.0418 7.79635 13.1498 7.88341 13.244C7.97047 13.1498 8.06895 13.0418 8.17655 12.9219C8.60416 12.4453 9.17295 11.7822 9.74032 11.0339C10.3092 10.2837 10.8678 9.45959 11.2817 8.66023C11.7021 7.84829 11.9414 7.1218 11.9414 6.55089C11.9414 3.97 9.88255 2.49292 7.88341 2.49292ZM7.88341 14.0871C7.47095 14.4945 7.47087 14.4944 7.47078 14.4943L7.46662 14.4901L7.45568 14.4789C7.44623 14.4693 7.43252 14.4552 7.41482 14.4369C7.37944 14.4004 7.32813 14.347 7.26316 14.2784C7.13325 14.1411 6.94854 13.9428 6.72727 13.6962C6.28532 13.2036 5.69469 12.5152 5.10264 11.7345C4.51213 10.9558 3.91123 10.0733 3.45557 9.19335C3.00643 8.32594 2.66602 7.40027 2.66602 6.55089C2.66602 3.21873 5.36081 1.3335 7.88341 1.3335C10.406 1.3335 13.1008 3.21873 13.1008 6.55089C13.1008 7.40027 12.7604 8.32594 12.3112 9.19335C11.8556 10.0733 11.2547 10.9558 10.6642 11.7345C10.0721 12.5152 9.48149 13.2036 9.03954 13.6962C8.81827 13.9428 8.63357 14.1411 8.50365 14.2784C8.43868 14.347 8.38737 14.4004 8.35199 14.4369C8.3343 14.4552 8.32059 14.4693 8.31114 14.4789L8.3002 14.4901L8.29632 14.494C8.29622 14.4941 8.29586 14.4945 7.88341 14.0871ZM7.88341 14.0871L8.29632 14.494C8.1874 14.6043 8.0384 14.6668 7.88341 14.6668C7.72841 14.6668 7.57969 14.6046 7.47078 14.4943L7.88341 14.0871Z" fill="${
      color ?? '#667994'
    }" opacity="${opacity}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.88341 5.39147C7.24308 5.39147 6.72399 5.91056 6.72399 6.55089C6.72399 7.19122 7.24308 7.71031 7.88341 7.71031C8.52374 7.71031 9.04283 7.19122 9.04283 6.55089C9.04283 5.91056 8.52374 5.39147 7.88341 5.39147ZM5.56457 6.55089C5.56457 5.27023 6.60275 4.23205 7.88341 4.23205C9.16407 4.23205 10.2022 5.27023 10.2022 6.55089C10.2022 7.83155 9.16407 8.86973 7.88341 8.86973C6.60275 8.86973 5.56457 7.83155 5.56457 6.55089Z" fill="${
      color ?? '#667994'
    }" opacity="${opacity}"/>
</svg>`}
    width={size ?? 16}
    height={size ?? 16}
  />
);

const sizess = Dimensions.get('screen');

export default function MapGeolocationView(props: any) {
  const mapRef = useRef<any>(null);
  const [currentRegion, setcurrentRegion] = useState<any>();

  const [isLoading, setIsLoading] = useState(false);
  const [address, setAddress] = useState<any>(null);

  useEffect(() => {
    animatedCurrentRegion();
  }, []);

  const animatedCurrentRegion = () => {
    Geolocation.getCurrentPosition(position => {
      getAddressFromCoordinates(
        position.coords.latitude,
        position.coords.longitude,
      );
      mapRef.current.animateToRegion({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
      setcurrentRegion({
        latitude: position.coords.latitude ?? 21.040531,
        longitude: position.coords.longitude ?? 105.774083,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    });
  };

  const getAddressFromCoordinates = async (lat: number, lng: number) => {
    setIsLoading(true);
    const GEOCODING_API_URL = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=AIzaSyBrjZpmgCpST9GWPt7fCnr_EiQi-uL9SQM`;
    try {
      const response = await fetch(GEOCODING_API_URL);
      const data = await response.json();
      if (data && data.results && data.results.length > 0) {
        // console.log('Address:', data.results[0]);
        setAddress(
          Ultis.substringLastForMap(data.results[0].formatted_address),
        );
      } else {
        console.error('Geocoding API error:', data.status);
      }
    } catch (error) {
      console.error('Geocoding request error:', error);
    }
    setIsLoading(false);
  };

  return (
    <View style={styles.container}>
      <View
        style={{
          zIndex: 111,
          paddingTop: 35,
          width: '100%',
          backgroundColor: '#ffffff',
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <TouchableOpacity
            style={{
              paddingVertical: 12,
              paddingHorizontal: 16,
              flexDirection: 'row',
              alignItems: 'center',
              gap: 8,
            }}
            onPress={props.onBack}>
            <FontAwesomeIcon icon={faAngleLeft} size={24} color="#00204D99" />
            <Text style={[TypoSkin.title3]}>Chọn địa điểm</Text>
          </TouchableOpacity>
          <View style={{paddingHorizontal: 8}}>
            <TouchableOpacity
              style={{paddingRight: 16}}
              onPress={() => {
                props.onDone({
                  formatted_address: address,
                  geometry: {
                    location: {
                      lat: currentRegion.latitude,
                      lng: currentRegion.longitude,
                    },
                  },
                });
              }}>
              <Text
                style={[
                  TypoSkin.title3,
                  {color: ColorThemes.light.primary_main_color},
                ]}>
                Xong
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <View
          style={{
            gap: 16,
            paddingHorizontal: 16,
            paddingVertical: 8,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            borderTopWidth: 1,
            borderTopColor: ColorThemes.light.neutral_main_border_color,
          }}>
          <OutlineLocation
            color={ColorThemes.light.primary_main_color}
            size={24}
          />
          <Text
            style={[
              TypoSkin.title3,
              {color: ColorThemes.light.neutral_text_subtitle_color, flex: 1},
            ]}
            numberOfLines={2}>
            {isLoading ? 'Đang tìm địa chỉ...' : address ?? ''}
          </Text>
        </View>
      </View>
      {/* map view */}
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={currentRegion}
        zoomEnabled={true}
        showsUserLocation={true}
        zoomControlEnabled={true}
        onRegionChangeComplete={region => {
          setcurrentRegion(region);
          getAddressFromCoordinates(region.latitude, region.longitude);
        }}
      />
      <View
        style={{
          marginBottom: 16,
          position: 'absolute',
          left: sizess.width / 2 - 16,
          top: sizess.height / 2 - 16,
        }}>
        <FontAwesomeIcon
          icon={faLocation}
          color={ColorThemes.light.error_main_color}
          size={32}
        />
      </View>
      <View style={{flexDirection: 'row', alignItems: 'flex-end'}}>
        <TouchableOpacity
          onPress={animatedCurrentRegion}
          style={{
            padding: 12,
            margin: 24,
            backgroundColor: ColorThemes.light.primary_main_color,
            borderRadius: 40,
          }}>
          <FontAwesomeIcon
            icon={faLocationArrow}
            color={ColorThemes.light.white}
            size={28}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    gap: 16,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
    position: 'absolute',
  },
  ViewPitchContainer: {
    height: 150,
    paddingBottom: 16,
    width: '100%',
    backgroundColor: ColorThemes.light.white,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowOpacity: 1,
    elevation: 20,
    shadowRadius: 20,
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowColor: 'rgba(0, 0, 0, 0.03)',
  },
  viewPitchCard: {
    backgroundColor: 'red',
    borderRadius: 8,
  },

  searchContainer: {
    width: '100%',
    flexDirection: 'row',
    backgroundColor: '#fff',
    gap: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
});
