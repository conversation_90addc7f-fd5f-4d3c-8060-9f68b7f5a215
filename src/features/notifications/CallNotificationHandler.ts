import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import { navigate, RootScreen } from '../../router/router';
import IncomingCallOverlayService from '../../services/IncomingCallOverlayService';
import WebRTCService from '../call/WebRTCService';
import { showSnackbar, ComponentStatus } from 'wini-mobile-components';

interface CallNotificationData {
  type: string;
  callerId: string;
  callerName: string;
  callerAvatar?: string;
  callHistoryId: string;
}

class CallNotificationHandler {
  private static instance: CallNotificationHandler;
  private isInitialized = false;

  private constructor() {}

  static getInstance(): CallNotificationHandler {
    if (!CallNotificationHandler.instance) {
      CallNotificationHandler.instance = new CallNotificationHandler();
    }
    return CallNotificationHandler.instance;
  }

  // Khởi tạo call notification handling
  initialize(): void {
    if (this.isInitialized) {
      return;
    }

    this.setupCallNotifications();
    this.isInitialized = true;
    console.log('✅ CallNotificationHandler initialized');
  }

  // Setup các listener cho call notifications
  private setupCallNotifications(): void {
    // Xử lý khi app đang chạy foreground
    messaging().onMessage(async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
      console.log('📱 Received foreground notification:', remoteMessage);
      
      if (remoteMessage.data?.type === 'incoming_call') {
        this.handleIncomingCallNotification(remoteMessage.data as CallNotificationData);
      }
    });

    // Xử lý khi app ở background/quit và user tap notification
    messaging().onNotificationOpenedApp((remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
      console.log('📱 App opened from background notification:', remoteMessage);
      
      if (remoteMessage.data?.type === 'incoming_call') {
        this.handleCallNotificationTap(remoteMessage.data as CallNotificationData);
      }
    });

    // Xử lý khi app được mở từ quit state bởi notification
    messaging()
      .getInitialNotification()
      .then((remoteMessage: FirebaseMessagingTypes.RemoteMessage | null) => {
        if (remoteMessage?.data?.type === 'incoming_call') {
          console.log('📱 App opened from quit state by notification:', remoteMessage);
          this.handleCallNotificationTap(remoteMessage.data as CallNotificationData);
        }
      });
  }

  // Xử lý incoming call notification khi app đang chạy
  private handleIncomingCallNotification(data: CallNotificationData): void {
    console.log('📞 Handling incoming call notification:', data);

    // Kiểm tra xem có đang trong cuộc gọi khác không
    if (WebRTCService.isInCall()) {
      console.log('📞 Already in call, ignoring notification');
      return;
    }

    // Hiển thị incoming call UI
    IncomingCallOverlayService.showIncomingCall({
      callerName: data.callerName,
      callerAvatar: data.callerAvatar,
      callerId: data.callerId,
    });

    // Set callHistoryId cho WebRTC service
    if (data.callHistoryId) {
      // TODO: Add method to WebRTCService to set callHistoryId
      console.log('📞 Setting callHistoryId:', data.callHistoryId);
    }
  }

  // Xử lý khi user tap notification để mở app
  private handleCallNotificationTap(data: CallNotificationData): void {
    console.log('📞 Handling call notification tap:', data);

    // Delay một chút để đảm bảo app đã load xong
    setTimeout(() => {
      // Kiểm tra xem cuộc gọi còn valid không (chưa timeout)
      const callAge = this.getCallAge(data);
      
      if (callAge > 60) {
        // Cuộc gọi đã quá 60 giây - hiển thị missed call
        this.showMissedCallMessage(data);
        return;
      }

      // Navigate đến CallScreen
      navigate(RootScreen.CallScreen, {
        isIncoming: true,
        callerId: data.callerId,
        callerName: data.callerName,
        callerAvatar: data.callerAvatar,
      });

      // Auto-accept call hoặc hiển thị UI để user chọn
      this.handleLateCallResponse(data);
    }, 1000);
  }

  // Tính tuổi của cuộc gọi (giả sử có timestamp trong data)
  private getCallAge(data: CallNotificationData): number {
    // TODO: Implement based on actual data structure
    // Có thể lấy từ callHistoryId hoặc timestamp trong notification
    return 0; // Placeholder
  }

  // Hiển thị thông báo missed call
  private showMissedCallMessage(data: CallNotificationData): void {
    showSnackbar({
      status: ComponentStatus.WARNING,
      message: `Cuộc gọi nhỡ từ ${data.callerName}`,
    });

    // TODO: Navigate to CallHistory hoặc hiển thị missed call screen
    console.log('📞 Showing missed call for:', data.callerName);
  }

  // Xử lý phản hồi muộn cho cuộc gọi
  private handleLateCallResponse(data: CallNotificationData): void {
    // Có thể auto-accept hoặc hiển thị dialog cho user chọn
    // Tùy thuộc vào business logic

    // Option 1: Auto-accept
    // setTimeout(() => {
    //   WebRTCService.acceptCall();
    // }, 500);

    // Option 2: Show dialog
    // Alert.alert(
    //   'Cuộc gọi đến',
    //   `${data.callerName} đã gọi cho bạn. Bạn có muốn tham gia cuộc gọi?`,
    //   [
    //     { text: 'Từ chối', onPress: () => WebRTCService.rejectCall() },
    //     { text: 'Chấp nhận', onPress: () => WebRTCService.acceptCall() },
    //   ]
    // );

    console.log('📞 Handling late call response for:', data.callerName);
  }

  // Tạo notification channel cho incoming calls (Android)
  static async createCallNotificationChannel(): Promise<void> {
    try {
      const notifee = require('@notifee/react-native').default;
      
      await notifee.createChannel({
        id: 'incoming_calls',
        name: 'Incoming Calls',
        importance: 4, // HIGH
        sound: 'default',
        vibration: true,
        lights: true,
        lightColor: '#FF0000',
      });

      console.log('✅ Call notification channel created');
    } catch (error) {
      console.error('❌ Failed to create call notification channel:', error);
    }
  }

  // Request notification permissions
  static async requestNotificationPermissions(): Promise<boolean> {
    try {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('✅ Notification permissions granted');
        
        // Tạo notification channel
        await this.createCallNotificationChannel();
        
        return true;
      } else {
        console.log('❌ Notification permissions denied');
        return false;
      }
    } catch (error) {
      console.error('❌ Error requesting notification permissions:', error);
      return false;
    }
  }

  // Lấy FCM token để gửi notifications
  static async getFCMToken(): Promise<string | null> {
    try {
      const token = await messaging().getToken();
      console.log('📱 FCM Token:', token);
      return token;
    } catch (error) {
      console.error('❌ Error getting FCM token:', error);
      return null;
    }
  }

  // Cleanup
  cleanup(): void {
    this.isInitialized = false;
    console.log('🧹 CallNotificationHandler cleaned up');
  }
}

export default CallNotificationHandler.getInstance();
