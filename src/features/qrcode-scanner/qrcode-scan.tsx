import React, {forwardRef, useEffect, useRef, useState} from 'react';

import {
  StyleSheet,
  Text,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  View,
} from 'react-native';
import {ColorThemes} from '../../assets/skin/colors';
import {useNavigation} from '@react-navigation/native';
import {
  Camera,
  useCameraDevice,
  useCodeScanner,
} from 'react-native-vision-camera';
import {
  closePopup,
  showSnackbar,
  ComponentStatus,
  FLoading,
  Winicon,
} from 'wini-mobile-components';
import ScreenHeader from '../../Screen/Layout/header';

export const PopupQrcodeScan = forwardRef(function PopupQrcodeScan(
  data: {onDone?: any},
  ref: any,
) {
  const {onDone} = data;
  const navigation = useNavigation<any>();
  const camera = useRef<Camera>(null);

  const [isTorchOn, setIsTorchOn] = useState<any>('off');
  const [isLoading, setLoading] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const device = useCameraDevice('back');
  const codeScanner = useCodeScanner({
    codeTypes: ['qr'],
    onCodeScanned: async (codes: any) => {
      // console.log(`onCodeScanned `, codes);
      // console.log(`onCodeScanned value`, codes[0].value);

      if (codes[0].value) {
        const data = codes[0].value;
        // check data == 10 characters
        if (data?.length == 10) {
          closePopup(ref);
          setLoading(false);
          onDone(data);
        } else {
          showSnackbar({
            message: 'Dữ liệu quét không đúng',
            status: ComponentStatus.ERROR,
          });
          closePopup(ref);
          setLoading(false);
        }
      } else {
        showSnackbar({
          message: 'Dữ liệu quét không đúng',
          status: ComponentStatus.ERROR,
        });
        closePopup(ref);
        setLoading(false);
      }
    },
  });

  useEffect(() => {
    // exception case
    setRefresh(!refresh);
  }, [device, hasPermission]);

  useEffect(() => {
    const requestCameraPermission = async () => {
      const permission = await Camera.requestCameraPermission();
      // console.log("Camera.requestCameraPermission ", permission);
      setHasPermission(permission === 'granted');
    };

    requestCameraPermission();

    // if it is idle for 15 secs, it will be closed
    // setTimeout(() => {
    //     if (ref) closePopup(ref);
    // }, 15 * 1000);
  }, []);

  if (device == null || !hasPermission) {
    return (
      <View
        style={{
          flex: 1,
          position: 'absolute',
          height: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Text style={{backgroundColor: 'white'}}>
          Camera not available or not permitted
        </Text>
      </View>
    );
  }

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height,
        backgroundColor: '#fff',
      }}>
      <FLoading visible={isLoading} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
          flexDirection: 'row',
          paddingVertical: 4,
          paddingHorizontal: 8,
        }}
        title={`Quét Qrcode`}
        prefix={
          <TouchableOpacity
            onPress={() => setIsTorchOn(isTorchOn == 'on' ? 'off' : 'on')}
            style={{padding: 8, alignItems: 'center'}}>
            <Winicon
              src={
                isTorchOn == 'on'
                  ? 'fill/buildings/flashlight'
                  : 'outline/buildings/flashlight'
              }
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
        action={
          <TouchableOpacity
            onPress={() => closePopup(ref)}
            style={{padding: 8, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <View
        style={{
          flex: 1,
          height: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Camera
          ref={camera}
          codeScanner={codeScanner}
          style={StyleSheet.absoluteFill}
          device={device}
          isActive={true}
          torch={isTorchOn}
        />
      </View>
    </SafeAreaView>
  );
});
