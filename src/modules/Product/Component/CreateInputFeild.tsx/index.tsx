import { CreateProductStyles } from "../../styles/CreateProductStyles"
import { Controller } from "react-hook-form"
import { CreateInputFeildProps } from "../../types"
import { Text, TextInput, View } from "react-native"
export const CreateInputFeild = (props: CreateInputFeildProps) => {
    return <>
        <View style={CreateProductStyles.section}>
            <View
                style={CreateProductStyles.textField}>
                <Text style={CreateProductStyles.label}>{props.nameFeild} *</Text>
                {props.maxlength ?
                    <Text style={CreateProductStyles.limit}>
                        {props.checkLengthText}/{props.maxlength}
                    </Text>
                    :
                    null
                }
            </View>
            <Controller
                control={props.control}
                name={props.name}
                rules={{
                    required: props.rule,
                }}
                render={({ field: { value, onChange } }) => (
                    props.maxlength ?
                        <TextInput
                            style={CreateProductStyles.textHeight}
                            placeholder={props.placeholder}
                            placeholderTextColor="#DDDDDD"
                            maxLength={props.maxlength}
                            value={value}
                            onChange={e => onChange(e.nativeEvent.text)}
                            multiline={props.mutiline}
                        />
                        :
                        <TextInput
                            style={CreateProductStyles.textHeight}
                            placeholder={props.placeholder}
                            placeholderTextColor="#DDDDDD"
                            value={value}
                            onChange={e => onChange(e.nativeEvent.text)}
                            multiline={props.mutiline}
                        />
                )}
            />
        </View>
    </>
}