import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Pressable,
  Image,
} from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { Radio, RadioAction } from '../../../components/Field/Radio';
import { LabelProductStyles } from '../styles/LabelProductStyles';
import { ListItemProps } from '../types';
const ListItemLable = (props: ListItemProps) => {
  const { setSelecChildID, setSelecChildName, dataLabel } = props;
  const [isSelected, setIsSelected] = useState('');
  const [data, setData] = useState<any[]>();
  useEffect(() => {
    if (dataLabel && dataLabel?.length > 0) {
      setData(dataLabel);
    }
  }, [dataLabel]);

  const handleSelectLabel = (item: any) => {
    setIsSelected(item?.Id);
    setSelecChildID(item?.Id);
    setSelecChildName(item?.Name);
  };
  return (
    <FlatList
      data={data}
      style={LabelProductStyles.Container}
      keyExtractor={(item, i) => `${i} ${item.Id}`}
      renderItem={({ item, index }) => (
        <Pressable style={LabelProductStyles.LabelContainer}>
          <View style={LabelProductStyles.LabelText}>
            <Image
              source={{
                uri: item.Img,
              }}
              style={LabelProductStyles.LabelImage}
            />
            <TouchableOpacity onPress={() => handleSelectLabel(item)}>
              <Text style={LabelProductStyles.LabelName}>{item.Name}</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity onPress={() => handleSelectLabel(item)}>
            {isSelected == item.Id ? <RadioAction /> : <Radio />}
          </TouchableOpacity>
        </Pressable>
      )}
    />
  );
};
export default ListItemLable;
