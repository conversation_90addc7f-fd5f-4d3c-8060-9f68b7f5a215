import React, { useEffect, useState } from 'react';
import { FlatList } from 'react-native-gesture-handler';
import ListItemCard from '../card/ProductCreateCard';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store/store';
import { ListItemProps } from '../types';
const ListItem = (props: ListItemProps) => {
  const {
    setSelecChildID,
    setSelecChildName,
    selectItemChild,
    setSelectItemChild,
    dataCategory: propDataCategory,
    backSelect,
    setBackSelect,
  } = props;
  const [isSelected, setIsSelected] = useState('');
  const [data, setData] = useState<any[]>();
  const { data: dataCategory } = useSelector(
    (state: RootState) => state.category,
  );
  const categoryData = propDataCategory || dataCategory;
  const handleSelect = (item: any) => {
    setSelecChildID(item?.Id);
    setSelecChildName(item?.Name);
    if (item?.Id !== isSelected) {
      setIsSelected(item.Id);
    } else {
      setIsSelected('');
    }
  };
  useEffect(() => {
    if (selectItemChild) {
      if (selectItemChild?.Children && selectItemChild?.Children?.length > 0) {
        setData(selectItemChild?.Children);
      } else {
        setData(categoryData);
      }
    }
  }, [selectItemChild]);
  useEffect(() => {
    if (backSelect) {
      setData(categoryData);
      setBackSelect && setBackSelect(false);
    }
  }, [backSelect]);
  return (
    // lấy danh hiển thị danh sách danh mục sản phẩm
    <FlatList
      data={data ? data : categoryData}
      style={{ flex: 1, marginBottom: 50 }}
      keyExtractor={(item, i) => `${i} ${item.Id}`}
      renderItem={({ item, index }) =>
        ListItemCard(
          { item, index },
          isSelected,
          handleSelect,
          selectItemChild,
          setSelectItemChild,
        )
      }
    />
  );
};
export default ListItem;
