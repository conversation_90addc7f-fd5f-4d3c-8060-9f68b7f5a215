/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { Title } from '../../Config/Contanst';
import ManageItemProduct from './Component/ManageProduct';
import { InforHeader } from '../../Screen/Layout/headers/inforHeader';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../assets/skin/colors';
import PopupSearchProduct from './Popup/PopupSearchProduct';
import { useSelectorProductState } from '../../redux/hook/productHook ';
import { Product } from '../../redux/models/product';
import { ManageProductStyles } from './styles/ManageProductStyles';
const ManageProduct = () => {
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const { data } = useSelectorProductState();
  const [filteredData, setFilteredData] = useState(data);
  useEffect(() => {
    setFilteredData(data);
  }, [data]);
  const handleSearch = (searchData: {
    name: string;
    stock: string;
    price: string;
  }) => {
    if (!searchData.name && !searchData.stock && !searchData.price) {
      setFilteredData(data);
      setIsSearchVisible(false);
      return;
    }
    const lowercasedName = searchData.name.toLowerCase();
    const newFilteredData = JSON.parse(JSON.stringify(data));
    newFilteredData.forEach((category: any) => {
      const filteredProducts = category.data.filter((product: Product) => {
        const nameMatch = searchData.name
          ? product.Name.toLowerCase().includes(lowercasedName)
          : true;

        let stockMatch = true;
        if (searchData.stock) {
          const stockValue = product.InStock;
          if (stockValue === null || stockValue === undefined) {
            stockMatch = false;
          } else {
            switch (searchData.stock) {
              case '1':
                stockMatch = stockValue < 100;
                break;
              case '2':
                stockMatch = stockValue > 100 && stockValue <= 500;
                break;
              case '3':
                stockMatch = stockValue > 500 && stockValue <= 1000;
                break;
              case '4':
                stockMatch = stockValue > 1000;
                break;
              default:
                stockMatch = true;
                break;
            }
          }
        }
        let priceMatch = true;
        if (searchData.price) {
          const productPrice = product.Price;
          if (productPrice === null || productPrice === undefined) {
            priceMatch = false;
          } else {
            switch (searchData.price) {
              case '1':
                priceMatch = productPrice < 1000000;
                break;
              case '2':
                priceMatch = productPrice > 1000000 && productPrice <= 5000000;
                break;
              case '3':
                priceMatch = productPrice > 5000000 && productPrice <= 10000000;
                break;
              case '4':
                priceMatch = productPrice > 10000000;
                break;
              default:
                priceMatch = true;
                break;
            }
          }
        }
        return nameMatch && stockMatch && priceMatch;
      });
      category.data = filteredProducts;
      category.number = filteredProducts.length;
    });

    setFilteredData(newFilteredData);
    setIsSearchVisible(false);
  };
  return (
    <View style={ManageProductStyles.container}>
      {/* Header */}
      <InforHeader
        title={Title.MyProduct}
        showSearch={true}
        showAction={true}
        customActions={
          <TouchableOpacity
            onPress={() => setIsSearchVisible(true)}
            style={ManageProductStyles.manageProductInfoHeaderButton}>
            <Winicon
              src="outline/user interface/search"
              size={24}
              color={ColorThemes.light.sub_text_color}
            />
          </TouchableOpacity>
        }
      />
      <ManageItemProduct data={filteredData} />
      <PopupSearchProduct
        isVisible={isSearchVisible}
        onClose={() => setIsSearchVisible(false)}
        onSearch={handleSearch}
      />
    </View>
  );
};

export default ManageProduct;
