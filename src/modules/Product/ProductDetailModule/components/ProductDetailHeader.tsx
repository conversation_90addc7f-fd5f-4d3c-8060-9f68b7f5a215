import React from 'react';
import {View, TouchableOpacity, StyleSheet} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import CartIcon from '../../../../components/CartIcon';
import {ColorThemes} from '../../../../assets/skin/colors';
import {ProductDetailHeaderProps} from '../types';
import {commonStyles} from '../styles';

const ProductDetailHeader: React.FC<ProductDetailHeaderProps> = ({
  onBack,
  onCartPress,
  onMenuPress,
}) => {
  return (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        activeOpacity={0.7}
        hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
        onPress={onBack}>
        <View style={[commonStyles.headerIconWrapper, {padding: 6}]}>
          <Winicon
            src="outline/arrows/left-arrow"
            size={20}
            color={ColorThemes.light.neutral_text_title_color}
          />
        </View>
      </TouchableOpacity>
      <View style={styles.headerRight}>
        <TouchableOpacity style={styles.cartButton} onPress={onCartPress}>
          <View style={[commonStyles.headerIconWrapper, {padding: 4}]}>
            <CartIcon color="#1C33FF" size={20} isHome={false} />
          </View>
        </TouchableOpacity>
        <TouchableOpacity onPress={onMenuPress}>
          <View style={[commonStyles.headerIconWrapper, {padding: 8}]}>
            <Winicon
              src="fill/layout/dots-vertical"
              size={20}
              color="#1C33FF"
            />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 30,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  cartButton: {
    position: 'relative',
  },
  backButton: {
    paddingRight: 16,
    paddingVertical: 8,
    alignItems: 'center',
    zIndex: 10,
  },
});

export default ProductDetailHeader;
