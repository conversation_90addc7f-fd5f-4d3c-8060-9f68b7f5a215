import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {ProductInfoSectionProps} from '../types';
import {TypoSkin} from '../../../../assets/skin/typography';

const ProductInfoSection: React.FC<ProductInfoSectionProps> = ({
  brandName,
  sold,
  inStock,
}) => {
  return (
    <View style={styles.detailsContainer}>
      <View style={styles.detailsHorizontalContainer}>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Hãng: {brandName || '-'}</Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Đã bán: {sold || 0}</Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Còn: {inStock || 0}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  detailsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  detailsHorizontalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    flex: 1,
  },
  detailItem: {
    flexDirection: 'column',
    alignItems: 'center',
    paddingVertical: 4,
  },
  detailLabel: {
    ...TypoSkin.body3,
    fontWeight: '400',
  },
});

export default ProductInfoSection;
