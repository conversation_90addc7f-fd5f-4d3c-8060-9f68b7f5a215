import {StyleSheet, Dimensions} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';

const {width} = Dimensions.get('window');

// Constants
export const ITEM_WIDTH = width * 0.32;
export const ITEM_SPACING = 10;

export const commonStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  scrollView: {
    flex: 1,
  },
  sectionContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sectionTitle: {
    ...TypoSkin.semibold3,
    marginBottom: 12,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flexGrow: {
    flex: 1,
  },
  headerIconWrapper: {
    gap: 4,
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    borderRadius: 20,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  // Skeleton styles
  skeletonCircle: {
    borderRadius: 20,
  },
  skeletonImage: {
    borderRadius: 0,
  },
});
