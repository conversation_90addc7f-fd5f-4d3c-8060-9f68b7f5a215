import {useCallback, useEffect} from 'react';
import ScrollableTabs from '../../../news/scrollable/ScrollableTabs';
import {
  faBookOpen,
  faFire,
  faHeart,
  faTruckFast,
} from '@fortawesome/free-solid-svg-icons';
import {useSelector} from 'react-redux';
import {RootState} from '../../../../redux/store/store';
import {useProductByCategoryHook} from '../../../../redux/reducers/ProductByCategoryReducer';
import {View} from 'react-native';

const TABS_DATA = [
  {
    id: 'IsHot',
    label: 'HOT',
    icon: faFire,
  },
  {
    id: 'IsFreeShip',
    label: 'Freeship',
    icon: faTruckFast,
    color: '#3FB993',
  },
  {
    id: 'IsNew',
    label: 'Mới',
    icon: faBookOpen,
  },
  {
    id: 'FavoriteBrand',
    label: 'Nhãn hàng ưa chuộng',
    icon: faHeart,
  },
];

const ScrollOption = () => {
  const productByCategoryHook = useProductByCategoryHook();
  const {filter} = useSelector((state: RootState) => state.productByCategory);

  // // Handle filter change
  const handleFilterChange = useCallback(
    (filterId: string) => {
      const newActiveFilters = {
        [filterId]: true,
      };
      productByCategoryHook.setData('filter', {
        ...filter,
        activeFilters: newActiveFilters,
      });
    },
    [filter, productByCategoryHook],
  );

  useEffect(() => {
    handleFilterChange('IsHot');
  }, []);

  return (
    <View style={{marginHorizontal: 16}}>
      <ScrollableTabs onChangeTab={handleFilterChange} data={TABS_DATA} />
    </View>
  );
};

export default ScrollOption;
