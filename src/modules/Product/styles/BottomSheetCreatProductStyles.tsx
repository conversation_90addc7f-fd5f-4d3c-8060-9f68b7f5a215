import { StyleSheet, Dimensions } from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';


const { width } = Dimensions.get('window');

// Constants
export const ITEM_WIDTH = width * 0.32;
export const ITEM_SPACING = 10;

export const BottomSheetCreatProductStyles = StyleSheet.create({
    header: {
        width: '100%',
        height: Dimensions.get('window').height - 100,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        position: 'relative',
    },
    search: {
        padding: 10,
        backgroundColor: '#fff',
    },
    searchContent: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 10,
    },
    searchInput: {
        flex: 1,
        backgroundColor: '#f0f0f0',
        borderRadius: 5,
        padding: 8,
        marginRight: 10,
    },
    searchCancelButton: {
        color: '#007AFF',
        fontSize: 16,
    },
    footerSheet: {
        flex: 1,
        marginBottom: 10,
        position: 'absolute',
        bottom: 2,
        width: '100%',
    },
    bodySheet: {
        height: 1000,
        marginBottom: 50,
    },
    searchData: {
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1
    },
    notFoundData: {
        color: '#666',
        ...TypoSkin.body2,
    },
    cancelSheetButton: {
        flexDirection: 'row',
        width: '100%',
        alignItems: 'center',
        justifyContent: 'space-around',
        position: 'absolute',
        bottom: 30,
    },
    cancelButton: {
        backgroundColor: '#f0f0f0', // Màu xám nhạt cho nút "Đóng"
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: '#ccc',
        width: '45%',
    },
    confirmButton: {
        backgroundColor: ColorThemes.light.primary_main_color, // Màu xanh cho nút "Xác nhận"
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 5,
        width: '45%',
    },
    buttonText: {
        color: ColorThemes.light.neutral_text_subtitle_color,
        fontSize: 16,
        fontWeight: '500',
        textAlign: 'center',
    },
    buttonTextConfirm: {
        color: ColorThemes.light.neutral_absolute_background_color,
        fontSize: 16,
        fontWeight: '500',
        textAlign: 'center',
    },
});
