import { StyleSheet, Dimensions } from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';


const { width } = Dimensions.get('window');

// Constants
export const ITEM_WIDTH = width * 0.32;
export const ITEM_SPACING = 10;

export const productStyles = StyleSheet.create({
    //  css CreateNewProduct
    containerCreateNewProduct: {
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    },
});
