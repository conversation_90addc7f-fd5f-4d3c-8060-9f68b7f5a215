import { StyleSheet, Dimensions } from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';


const { width } = Dimensions.get('window');

// Constants
export const ITEM_WIDTH = width * 0.32;
export const ITEM_SPACING = 10;

export const ManageProductStyles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_text_stable_color,
    },
    manageProductInfoHeaderButton: {
        minWidth: 60,
        flexDirection: 'row',
        marginRight: 15,
        gap: 2,
    },
    containerScreenManageProduct: {
        flex: 1,
        margin: 10,
        position: 'relative',
        paddingBottom: 100,
    },
    ScreenManageProductButton: {
        backgroundColor: 'blue',
        width: '80%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 50,
        borderRadius: 30,
        margin: 'auto',
        marginBottom: 20,
        position: 'absolute',
        bottom: 0,
        left: '10%',
    },
    ScreenManageProductTextButton: {
        color: ColorThemes.light.neutral_text_stable_color,
        fontSize: 16,
        fontWeight: '500',
        textAlign: 'center',
    },
    HeaderMenuManageProduct: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 10,
    },
    tabMenuManageProduct: {
        padding: 5,
    },
    textMenuManageProduct: {
        ...TypoSkin.title5,
        margin: "auto",
        color: ColorThemes.light.neutral_text_placeholder_reverse_color
    },
    textActionMenuManageProduct: {
        ...TypoSkin.title5,
        margin: "auto",
        color: ColorThemes.light.neutral_main_reverse_border
    }
});
