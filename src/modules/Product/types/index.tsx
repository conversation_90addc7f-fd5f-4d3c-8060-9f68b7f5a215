import { Control } from "react-hook-form";
import { ImageOrVideo } from "react-native-image-crop-picker";

export interface CreateProductProps {
    routeParams?: any;
}

export interface ProductState {
    image: ImageOrVideo[] | undefined;
    avataProduct: string;
    listProduct: string;
    label: string;
    lengText: {
        ProductName: number;
        DesProduct: number;
    };
}

export interface ProductFormData {
    image: string | null;
    ProductName: string;
    DesProduct: string;
    productType: string;
    label: string;
    listImage: string;
    price: string;
    InStock: string;
    freeship: boolean;
    content: string;
}

export interface ListItemProps {
    setSelecChildID: (item: any) => void;
    setSelecChildName: (item: any) => void;
    dataLabel: any[];
}

export interface ListItemProps {
    setSelecChildID: (item: any) => void;
    setSelecChildName: (item: any) => void;
    selectItemChild?: any;
    setSelectItemChild?: (item: any) => void;
    dataCategory?: any[];
    backSelect?: boolean;
    setBackSelect?: (item: boolean) => void;
}

export interface MenuProductProps {
    menu: string;
    setMenu: (menu: string) => void;
    data: any[]
}

export interface ListItemProps {
    setSelecChild: (item: any) => void;
}

export interface CreateInputFeildProps {
    control: Control<ProductFormData, any, ProductFormData>
    nameFeild: string
    checkLengthText?: number,
    name: string | any,
    rule: string | any
    placeholder: string
    maxlength?: number
    mutiline: boolean
}