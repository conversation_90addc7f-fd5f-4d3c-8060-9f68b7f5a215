// Interface cho CallHistory table
export interface CallHistory {
  Id: string;
  DateCreated: number; // timestamp
  Name: string; // Tên người gọi/nhận
  Receiver: string; // ID người nhận cuộc gọi
  IsAccept: boolean; // <PERSON><PERSON> chấp nhận cuộc gọi hay không
  Time: number; // Thời gian cuộc gọi (giây)
  CustomerId?: string; // ID người gọi (optional, có thể lấy từ context)
}

// Interface cho tạo CallHistory mới
export interface CreateCallHistoryRequest {
  Name: string;
  Receiver: string;
  CustomerId?: string;
}

// Interface cho cập nhật CallHistory
export interface UpdateCallHistoryRequest {
  Id: string;
  IsAccept?: boolean;
  Time?: number;
  EndReason?: 'completed' | 'rejected' | 'timeout' | 'cancelled';
}

// Interface cho response từ API
export interface CallHistoryResponse {
  data: CallHistory[];
  total: number;
}

// Interface cho call statistics
export interface CallStatistics {
  totalCalls: number;
  acceptedCalls: number;
  missedCalls: number;
  totalDuration: number; // tổng thời gian gọi (giây)
  averageDuration: number; // thời gian trung bình (giây)
}
