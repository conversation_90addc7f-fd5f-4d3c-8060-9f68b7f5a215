import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
} from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import FastImage from 'react-native-fast-image';
import CallHistoryAPI from '../../call/services/CallHistoryAPI';
import { CallHistory } from '../../call/types/CallTypes';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import ConfigAPI from '../../../Config/ConfigAPI';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';

const CallHistoryScreen: React.FC = () => {
  const [callHistory, setCallHistory] = useState<CallHistory[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const currentUser = useSelectorCustomerState().data;

  useEffect(() => {
    loadCallHistory();
  }, []);

  const loadCallHistory = async () => {
    if (!currentUser?.Id) {
      console.error('Current user not found');
      return;
    }

    try {
      setLoading(true);
      const response = await CallHistoryAPI.getCallHistory(currentUser.Id);
      setCallHistory(response.data);
      console.log('✅ Call history loaded:', response.data.length, 'records');
    } catch (error) {
      console.error('Error loading call history:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tải lịch sử cuộc gọi',
      });
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCallHistory();
    setRefreshing(false);
  };

  const formatDuration = (seconds: number): string => {
    if (seconds === 0) return 'Cuộc gọi nhỡ';

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes === 0) {
      return `${remainingSeconds} giây`;
    } else if (remainingSeconds === 0) {
      return `${minutes} phút`;
    } else {
      return `${minutes} phút ${remainingSeconds} giây`;
    }
  };

  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      // Hiển thị giờ nếu trong ngày
      return date.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      // Hiển thị ngày nếu quá 1 ngày
      return date.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit'
      });
    }
  };

  const getCallIcon = (callType: string) => {
    switch (callType) {
      case 'incoming':
        return '📞'; // Incoming call icon
      case 'outgoing':
        return '📞'; // Outgoing call icon
      case 'missed':
        return '📞'; // Missed call icon
      default:
        return '📞';
    }
  };

  const getCallIconColor = (callType: string) => {
    switch (callType) {
      case 'incoming':
        return '#4CAF50'; // Green for incoming
      case 'outgoing':
        return '#2196F3'; // Blue for outgoing
      case 'missed':
        return '#F44336'; // Red for missed
      default:
        return ColorThemes.light.neutral_text_secondary_color;
    }
  };

  const renderCallItem = ({ item }: { item: CallHistory }) => (
    <TouchableOpacity style={styles.callItem} activeOpacity={0.7}>
      <View style={styles.avatarContainer}>
        {/* TODO: Thêm avatar từ user data nếu có */}
        <View style={[styles.avatar, styles.defaultAvatar]}>
          <Text style={styles.avatarText}>
            {item.Name?.charAt(0).toUpperCase() || '?'}
          </Text>
        </View>
      </View>

      <View style={styles.callInfo}>
        <Text style={styles.callerName}>{item.Name || 'Unknown'}</Text>
        <Text style={styles.callDuration}>
          {formatDuration(item.Time || 0)}
        </Text>
      </View>

      <View style={styles.callDetails}>
        <Text style={styles.callTime}>{formatTime(item.DateCreated)}</Text>
        <TouchableOpacity
          style={[styles.callButton, { backgroundColor: getCallIconColor(item.CallType || 'missed') }]}
          activeOpacity={0.7}
        >
          <Text style={styles.callButtonIcon}>{getCallIcon(item.CallType || 'missed')}</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>Chưa có lịch sử cuộc gọi</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={callHistory}
        renderItem={renderCallItem}
        keyExtractor={(item) => item.Id}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={callHistory.length === 0 ? styles.emptyList : undefined}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[ColorThemes.light.primary_color]}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  callItem: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
    alignItems: 'center',
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  callInfo: {
    flex: 1,
  },
  callerName: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
    marginBottom: 4,
  },
  callDuration: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  callDetails: {
    alignItems: 'flex-end',
  },
  callTime: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_secondary_color,
    marginBottom: 8,
  },
  callButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  callButtonIcon: {
    fontSize: 16,
    color: 'white',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_secondary_color,
    textAlign: 'center',
  },
});

export default CallHistoryScreen;
