import {TextStyle, ViewStyle} from 'react-native';
import {BackgroundData} from '../../../../redux/models/PostBackground';

export interface PostData {
  Id?: string | number;
  Img?: string;
  Content?: string;
  Name?: string;
  Description?: string;
  LinkVideo?: string;
  relativeUser?: {
    title?: string;
    subtitle?: string;
    image?: string;
  };
  PostBackgroundM?: BackgroundData;
}

export interface ListItem {
  Id: string | number;
  title: string;
  icon?: string;
}

export interface TagItem {
  Id: string | number;
  title: string;
}

export interface DefaultPostProps {
  containerStyle?: ViewStyle;
  mainContainerStyle?: ViewStyle;
  imgStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  data: PostData;
  listItems?: ListItem[];
  listTags?: TagItem[];
  onPressSeeMore?: () => void;
  onPressDetail?: () => void;
  onPressHeader?: () => void;
  reportContent?: React.ReactNode;
  actionView?: React.ReactNode;
  trailingView?: React.ReactNode;
  horizontalList?: boolean;
  noDivider?: boolean;
  showContent?: boolean;
  dividerColor?: string;
}

export interface ImageData {
  hasImages: boolean;
  isMultiple?: boolean;
  imageUrls: string[];
  count: number;
}
