// App.js

import React, {useEffect, useState, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {BackgroundData} from '../../../../redux/models/PostBackground';
import {useSelector} from 'react-redux';
import {RootState} from '../../../../redux/store/store';
import FastImage from 'react-native-fast-image';

interface SwatchItemProps {
  item: BackgroundData;
  onPress: () => void;
  isSelected: boolean;
}

interface PickColorLineProps {
  onChoose?: (selectedColor: BackgroundData) => void;
  isReset?: boolean;
  initialBackgroundId?: string;
}

function splitColorString(color: string) {
  if (!color) return [];
  return color
    .split(',')
    .map(item => item.trim())
    .filter(Boolean)
    .filter(color => {
      // Ki<PERSON>m tra định dạng màu cơ bản
      return /^#[0-9A-Fa-f]{6}$|^#[0-9A-Fa-f]{3}$|^rgb\(|^rgba\(|^[a-zA-Z]+$/.test(
        color,
      );
    });
}

const renderTypeColor = (item: BackgroundData) => {
  if (!item.ColorMobile) return null;
  const colors = splitColorString(item.ColorMobile);
  if (colors.length === 0) return null;

  if (colors.length < 2) {
    const singleColor = colors[0];
    if (!singleColor) return null;
    return <View style={[styles.swatchBase, {backgroundColor: singleColor}]} />;
  }

  // Đảm bảo có ít nhất 2 màu hợp lệ cho gradient
  const validColors = colors.slice(0, 10); // Giới hạn số màu để tránh performance issues
  if (validColors.length < 2) return null;

  return (
    <LinearGradient
      colors={validColors}
      style={styles.swatchBase}
      start={{x: 0, y: 0}}
      end={{x: 0, y: 1}}
    />
  );
};

const SwatchItem: React.FC<SwatchItemProps> = ({item, onPress, isSelected}) => {
  const renderContent = () => {
    switch (item.Type) {
      case 999:
        return <View style={styles.swatchEmpty} />;
      case 1:
        return (
          <FastImage
            source={{uri: item.Img}}
            style={[styles.swatchBase]}
            resizeMode="cover"
          />
        );
      case 2:
        return renderTypeColor(item);
      default:
        return null;
    }
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.swatchContainer,
        // Thêm viền xanh để thể hiện mục đang được chọn
        isSelected && styles.selectedSwatch,
      ]}>
      {renderContent()}
    </TouchableOpacity>
  );
};

const PickColorLine: React.FC<PickColorLineProps> = ({
  onChoose,
  isReset,
  initialBackgroundId,
}) => {
  const {items, loading} = useSelector(
    (state: RootState) => state.postBackground,
  );

  // Sử dụng items từ Redux store
  const backgroundData = useMemo(() => items, [items]);

  // Dùng useState với lazy initialization
  const [selectedId, setSelectedId] = useState<string>(
    initialBackgroundId || '',
  );

  const handleColorSelect = (item: BackgroundData) => {
    setSelectedId(item.Id);
    onChoose?.(item);
  };

  // Cập nhật selectedId khi items được load lần đầu
  useEffect(() => {
    if (items.length > 0) {
      if (initialBackgroundId) {
        setSelectedId(initialBackgroundId);
      } else {
        setSelectedId(items[0].Id);
      }
    } else {
      setSelectedId('');
    }
  }, [items, initialBackgroundId]);

  useEffect(() => {
    if (isReset && backgroundData.length > 0) {
      setSelectedId(backgroundData[0].Id);
    }
  }, [isReset, backgroundData]);

  // Early return AFTER all hooks
  if (items.length <= 0) return null;

  // Hiển thị loading nếu đang tải data
  if (loading && items.length === 0) {
    return (
      <View style={styles.screen}>
        <View style={styles.container}>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>‹</Text>
          </TouchableOpacity>
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.screen}>
      <View style={styles.container}>
        {/* Nút điều hướng bên trái */}
        <TouchableOpacity style={styles.backButton}>
          <Text style={styles.backButtonText}>‹</Text>
        </TouchableOpacity>

        {/* Danh sách các lựa chọn */}
        <FlatList
          data={backgroundData}
          renderItem={({item}) => (
            <SwatchItem
              item={item}
              onPress={() => handleColorSelect(item)}
              isSelected={item.Id === selectedId}
            />
          )}
          keyExtractor={item => item.Id}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.flatList}
        />
      </View>
    </View>
  );
};

export default PickColorLine;

const SWATCH_SIZE = 44;
const SWATCH_MARGIN = 6;

const styles = StyleSheet.create({
  screen: {
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    backgroundColor: 'white',
    width: '100%',
  },
  backButton: {
    width: SWATCH_SIZE,
    height: SWATCH_SIZE,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#EAEAEA',
    borderRadius: 12,
    marginLeft: 10,
  },
  backButtonText: {
    fontSize: 28,
    color: '#555',
    lineHeight: 32,
  },
  flatList: {
    flex: 1,
    marginLeft: SWATCH_MARGIN,
  },
  swatchContainer: {
    width: SWATCH_SIZE,
    height: SWATCH_SIZE,
    marginHorizontal: SWATCH_MARGIN,
    borderRadius: 12,
    // Style cho viền khi được chọn
    borderWidth: 2,
    borderColor: 'transparent', // Mặc định trong suốt
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedSwatch: {
    borderColor: '#007AFF', // Màu viền xanh khi được chọn
  },
  swatchBase: {
    width: 40,
    height: 40,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden', // Đảm bảo gradient không tràn ra ngoài
  },
  swatchEmpty: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#CCCCCC',
  },
  emojiText: {
    fontSize: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: SWATCH_MARGIN,
  },
  loadingText: {
    color: '#555',
    fontSize: 14,
  },
});
