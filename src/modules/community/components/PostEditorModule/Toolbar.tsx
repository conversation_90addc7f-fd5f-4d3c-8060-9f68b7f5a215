import React from 'react';
import {View, TouchableOpacity} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import FormatIcon from './FormatIcon';
import {TextFormat} from './types';
import {styles} from './styles';
import {ColorThemes} from '../../../../assets/skin/colors';

type ToolbarProps = {
  format: TextFormat;
  toggleFormat: (key: 'bold' | 'italic' | 'underline') => void;
  handlePickImages: () => void;
};

const Toolbar: React.FC<ToolbarProps> = ({
  format,
  toggleFormat,
  handlePickImages,
}) => {
  return (
    <View style={styles.toolbar}>
      <View style={styles.formatButtons}>
        <FormatIcon
          label="B"
          active={format.bold}
          onPress={() => toggleFormat('bold')}
        />
        <FormatIcon
          label="I"
          active={format.italic}
          onPress={() => toggleFormat('italic')}
        />
        <FormatIcon
          label="U"
          active={format.underline}
          onPress={() => toggleFormat('underline')}
        />
      </View>

      <TouchableOpacity
        style={styles.imagePickerButton}
        onPress={handlePickImages}>
        <Winicon
          src="fill/development/image"
          size={20}
          color={ColorThemes.light.Neutral_Text_Color_Subtitle}
        />
      </TouchableOpacity>
    </View>
  );
};

export default Toolbar;
