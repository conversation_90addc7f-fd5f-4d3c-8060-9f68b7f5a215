import {useState, useRef, useCallback} from 'react';
import {TextInputSelectionChangeEventData} from 'react-native';
import {TextSegment, TextFormat} from '../types';
import {
  htmlToSegments,
  mergeAdjacentSegments,
  getFullText,
} from '../utils/textUtils';
import {
  isVietnameseComposing,
  isBasicLatinChar,
  isVietnameseChar,
  canReplaceLatinChar,
  isFirstVietnameseChar,
  getVietnameseCharMap,
} from '../utils/vietnameseUtils';

export const useTextSegments = (
  initialText: string = '',
  initialHtml: string = '',
) => {
  // Khởi tạo segments từ initialHtml nếu có, nếu không thì từ initialText
  const [segments, setSegments] = useState<TextSegment[]>(() => {
    if (initialHtml) {
      return htmlToSegments(initialHtml);
    }
    return [{text: initialText, bold: false, italic: false, underline: false}];
  });

  const [format, setFormat] = useState<TextFormat>({
    bold: false,
    italic: false,
    underline: false,
  });

  const [selection, setSelection] = useState<{start: number; end: number}>({
    start: 0,
    end: 0,
  });

  // Thêm state để theo dõi trạng thái nhập liệu
  const [lastInputChar, setLastInputChar] = useState<string>('');
  const [isComposingVietnamese, setIsComposingVietnamese] =
    useState<boolean>(false);

  // Hàm xử lý sự kiện selection change
  const handleSelectionChange = useCallback((event: any) => {
    const newSelection = event.nativeEvent.selection;
    if (newSelection) {
      setSelection(newSelection);
    }
  }, []);

  // Hàm xử lý khi nhấn nút định dạng
  const toggleFormat = useCallback(
    (key: 'bold' | 'italic' | 'underline') => {
      setFormat(prev => ({
        ...prev,
        [key]: !prev[key],
      }));

      // Áp dụng định dạng cho văn bản đã chọn
      applyFormatToSelection(key);
    },
    [selection],
  );

  // Hàm áp dụng định dạng cho văn bản đã chọn
  const applyFormatToSelection = (
    formatKey: 'bold' | 'italic' | 'underline',
  ) => {
    // Sử dụng state selection thay vì lấy từ input.props.selection
    if (!selection || selection.start === selection.end) {
      // Nếu không có văn bản được chọn, chỉ thay đổi định dạng cho văn bản mới
      return;
    }

    const selectionStart = selection.start;
    const selectionEnd = selection.end;

    setSegments(prev => {
      // Tạo bản sao mới của mảng segments
      const newSegs = [...prev];

      // Tìm các segment chứa vùng chọn
      let charCount = 0;
      let startSegmentIndex = -1;
      let startPositionInSegment = -1;
      let endSegmentIndex = -1;
      let endPositionInSegment = -1;

      // Tìm segment chứa điểm bắt đầu
      for (let i = 0; i < newSegs.length; i++) {
        const segmentLength = newSegs[i].text.length;
        if (
          charCount <= selectionStart &&
          selectionStart < charCount + segmentLength
        ) {
          startSegmentIndex = i;
          startPositionInSegment = selectionStart - charCount;
        }
        if (
          charCount <= selectionEnd &&
          selectionEnd <= charCount + segmentLength
        ) {
          endSegmentIndex = i;
          endPositionInSegment = selectionEnd - charCount;
          break;
        }
        charCount += segmentLength;
      }

      // Nếu không tìm thấy segment
      if (startSegmentIndex === -1 || endSegmentIndex === -1) {
        console.warn('Cannot find segments for selection');
        return newSegs;
      }

      // Trường hợp selection nằm trong một segment
      if (startSegmentIndex === endSegmentIndex) {
        const segment = newSegs[startSegmentIndex];
        const beforeText = segment.text.substring(0, startPositionInSegment);
        const selectedText = segment.text.substring(
          startPositionInSegment,
          endPositionInSegment,
        );
        const afterText = segment.text.substring(endPositionInSegment);

        // Tạo segment mới cho phần được chọn với định dạng đã toggle
        const newFormat = {
          bold: formatKey === 'bold' ? !segment.bold : segment.bold,
          italic: formatKey === 'italic' ? !segment.italic : segment.italic,
          underline:
            formatKey === 'underline' ? !segment.underline : segment.underline,
        };

        // Thay thế segment hiện tại bằng 3 segment: trước, đã chọn, sau
        const newSegments = [];

        if (beforeText) {
          newSegments.push({...segment, text: beforeText});
        }

        newSegments.push({
          text: selectedText,
          bold: newFormat.bold,
          italic: newFormat.italic,
          underline: newFormat.underline,
        });

        if (afterText) {
          newSegments.push({...segment, text: afterText});
        }

        // Thay thế segment cũ bằng các segment mới
        newSegs.splice(startSegmentIndex, 1, ...newSegments);
      }
      // Trường hợp selection trải qua nhiều segment
      else {
        // Xử lý segment đầu tiên
        const firstSegment = newSegs[startSegmentIndex];
        const beforeFirstText = firstSegment.text.substring(
          0,
          startPositionInSegment,
        );
        const selectedFirstText = firstSegment.text.substring(
          startPositionInSegment,
        );

        // Xử lý segment cuối cùng
        const lastSegment = newSegs[endSegmentIndex];
        const selectedLastText = lastSegment.text.substring(
          0,
          endPositionInSegment,
        );
        const afterLastText = lastSegment.text.substring(endPositionInSegment);

        // Tạo mảng segments mới
        const resultSegments = [];

        // Thêm phần trước selection của segment đầu tiên
        if (beforeFirstText) {
          resultSegments.push({...firstSegment, text: beforeFirstText});
        }

        // Thêm phần được chọn của segment đầu tiên với định dạng mới
        resultSegments.push({
          text: selectedFirstText,
          bold: formatKey === 'bold' ? !firstSegment.bold : firstSegment.bold,
          italic:
            formatKey === 'italic' ? !firstSegment.italic : firstSegment.italic,
          underline:
            formatKey === 'underline'
              ? !firstSegment.underline
              : firstSegment.underline,
        });

        // Xử lý các segment ở giữa
        for (let i = startSegmentIndex + 1; i < endSegmentIndex; i++) {
          const midSegment = newSegs[i];
          resultSegments.push({
            text: midSegment.text,
            bold: formatKey === 'bold' ? !midSegment.bold : midSegment.bold,
            italic:
              formatKey === 'italic' ? !midSegment.italic : midSegment.italic,
            underline:
              formatKey === 'underline'
                ? !midSegment.underline
                : midSegment.underline,
          });
        }

        // Thêm phần được chọn của segment cuối cùng với định dạng mới
        resultSegments.push({
          text: selectedLastText,
          bold: formatKey === 'bold' ? !lastSegment.bold : lastSegment.bold,
          italic:
            formatKey === 'italic' ? !lastSegment.italic : lastSegment.italic,
          underline:
            formatKey === 'underline'
              ? !lastSegment.underline
              : lastSegment.underline,
        });

        // Thêm phần sau selection của segment cuối cùng
        if (afterLastText) {
          resultSegments.push({...lastSegment, text: afterLastText});
        }

        // Thay thế các segment cũ bằng các segment mới
        newSegs.splice(
          startSegmentIndex,
          endSegmentIndex - startSegmentIndex + 1,
          ...resultSegments,
        );
      }

      // Hợp nhất các segment liền kề có cùng định dạng
      return mergeAdjacentSegments(newSegs);
    });
  };

  const onChangeText = useCallback(
    (newText: string) => {
      const fullText = getFullText(segments);

      // Nếu không có thay đổi, không cần xử lý
      if (newText === fullText) return;

      // Kiểm tra xem có phải đang nhập chữ tiếng Việt đầu tiên không
      if (isFirstVietnameseChar(newText)) {
        setSegments([
          {
            text: newText,
            bold: format.bold,
            italic: format.italic,
            underline: format.underline,
          },
        ]);
        setLastInputChar(newText);
        setIsComposingVietnamese(true);
        return;
      }

      // Kiểm tra xem có phải đang thay thế chữ cái Latin bằng chữ cái tiếng Việt không
      if (
        fullText.length === 1 &&
        newText.length === 1 &&
        /^[a-zA-Z]$/.test(fullText) &&
        /^[áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ]$/.test(
          newText,
        )
      ) {
        // Kiểm tra xem chữ cái tiếng Việt có thể thay thế chữ cái Latin không
        const latinChar = fullText.charAt(0);
        const vietnameseChar = newText.charAt(0);

        const canReplace = (function () {
          const map = getVietnameseCharMap();
          const lowerLatinChar = latinChar.toLowerCase();
          return (
            map[lowerLatinChar] && map[lowerLatinChar].test(vietnameseChar)
          );
        })();

        if (canReplace) {
          setSegments([
            {
              text: newText,
              bold: format.bold,
              italic: format.italic,
              underline: format.underline,
            },
          ]);
          setLastInputChar(newText);
          setIsComposingVietnamese(true);
          return;
        }
      }

      setSegments(prev => {
        // Tạo bản sao mới của mảng segments
        const newSegs = [...prev];

        // Kiểm tra mảng rỗng và khởi tạo nếu cần
        if (newSegs.length === 0) {
          return [
            {
              text: newText,
              bold: format.bold,
              italic: format.italic,
              underline: format.underline,
            },
          ];
        }

        // Trường hợp 1: Xóa văn bản
        if (newText.length < fullText.length) {
          // Tìm vị trí bắt đầu khác biệt
          let diffIndex = 0;
          while (
            diffIndex < newText.length &&
            newText[diffIndex] === fullText[diffIndex]
          ) {
            diffIndex++;
          }

          // Số ký tự bị xóa
          const deletedCharCount = fullText.length - newText.length;

          // Tìm segment chứa vị trí cần xóa
          let charCount = 0;
          let targetSegmentIndex = -1;
          let positionInSegment = -1;

          for (let i = 0; i < newSegs.length; i++) {
            const segmentLength = newSegs[i].text.length;
            if (
              charCount <= diffIndex &&
              diffIndex < charCount + segmentLength
            ) {
              targetSegmentIndex = i;
              positionInSegment = diffIndex - charCount;
              break;
            }
            charCount += segmentLength;
          }

          // Nếu không tìm thấy segment (hiếm khi xảy ra)
          if (targetSegmentIndex === -1) {
            console.warn('Cannot find segment to delete from');
            return newSegs;
          }

          // Xóa văn bản từ vị trí đã xác định
          const segment = newSegs[targetSegmentIndex];
          const beforeText = segment.text.substring(0, positionInSegment);
          const afterText = segment.text.substring(
            positionInSegment + deletedCharCount,
          );

          // Cập nhật segment hiện tại
          newSegs[targetSegmentIndex] = {
            ...segment,
            text: beforeText + afterText,
          };

          // Xóa các segment trống
          const filteredSegs = newSegs.filter(seg => seg.text.length > 0);

          // Đảm bảo luôn có ít nhất một segment
          if (filteredSegs.length === 0) {
            return [
              {
                text: '',
                bold: format.bold,
                italic: format.italic,
                underline: format.underline,
              },
            ];
          }

          return filteredSegs;
        }

        // Trường hợp 2: Thêm hoặc thay đổi văn bản

        // Tìm điểm khác biệt đầu tiên giữa hai chuỗi
        let diffIndex = 0;
        const minLength = Math.min(fullText.length, newText.length);

        while (
          diffIndex < minLength &&
          fullText[diffIndex] === newText[diffIndex]
        ) {
          diffIndex++;
        }

        // Xác định phần văn bản được thêm vào
        const addedText = newText.substring(diffIndex);

        // Tìm segment chứa điểm khác biệt
        let charCount = 0;
        let targetSegmentIndex = -1;

        for (let i = 0; i < newSegs.length; i++) {
          charCount += newSegs[i].text.length;
          if (charCount > diffIndex) {
            targetSegmentIndex = i;
            break;
          }
        }

        // Nếu không tìm thấy segment (điểm thay đổi ở cuối văn bản)
        if (targetSegmentIndex === -1) {
          // Thêm segment mới với định dạng hiện tại
          newSegs.push({
            text: addedText,
            bold: format.bold,
            italic: format.italic,
            underline: format.underline,
          });
          return mergeAdjacentSegments(newSegs);
        }

        // Xác định vị trí trong segment
        const segmentStartIndex =
          charCount - newSegs[targetSegmentIndex].text.length;
        const positionInSegment = diffIndex - segmentStartIndex;

        const currentSegment = newSegs[targetSegmentIndex];

        // Cải thiện kiểm tra composing cho tiếng Việt
        // Kiểm tra nếu đang thay đổi ký tự cuối cùng hoặc gần cuối của segment
        const isComposing =
          positionInSegment > 0 &&
          (positionInSegment >= currentSegment.text.length - 15 ||
            isVietnameseComposing(currentSegment.text, positionInSegment));

        // Xác định độ dài thay đổi
        const changeLength = newText.length - fullText.length;

        // Nếu thay đổi chỉ 1-2 ký tự và ở cuối segment, có thể là đang gõ tiếng Việt
        const isSmallChange =
          changeLength <= 2 && positionInSegment === currentSegment.text.length;

        if (isComposing || isSmallChange) {
          const beforeComposing = currentSegment.text.substring(
            0,
            positionInSegment,
          );
          const afterComposing = newText.substring(diffIndex);

          // Trường hợp 1: Ký tự đầu tiên có dấu
          if (
            positionInSegment === 0 &&
            afterComposing.length === 1 &&
            isVietnameseChar(afterComposing)
          ) {
            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: afterComposing,
            };
          }
          // Trường hợp 2: Thay thế ký tự không dấu bằng ký tự có dấu
          else if (
            currentSegment.text.length >= 1 &&
            positionInSegment === currentSegment.text.length &&
            afterComposing.length === 1 &&
            isVietnameseChar(afterComposing) &&
            isBasicLatinChar(
              currentSegment.text[currentSegment.text.length - 1],
            ) &&
            canReplaceLatinChar(
              currentSegment.text[currentSegment.text.length - 1],
              afterComposing,
            )
          ) {
            // Thay thế ký tự cuối cùng bằng ký tự có dấu
            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: currentSegment.text.slice(0, -1) + afterComposing,
            };
          }
          // Trường hợp 3: Thay thế ký tự có dấu bằng ký tự có dấu khác (ví dụ: ă -> â)
          else if (
            currentSegment.text.length >= 1 &&
            positionInSegment === currentSegment.text.length &&
            afterComposing.length === 1 &&
            isVietnameseChar(afterComposing) &&
            isVietnameseChar(
              currentSegment.text[currentSegment.text.length - 1],
            )
          ) {
            // Thay thế ký tự cuối cùng bằng ký tự có dấu mới
            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: currentSegment.text.slice(0, -1) + afterComposing,
            };
          } else {
            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: beforeComposing + afterComposing,
            };
          }
        } else {
          // Trường hợp thêm mới: Tách segment hiện tại và thêm segment mới

          // Cắt segment hiện tại tại điểm thay đổi
          const firstPart = currentSegment.text.substring(0, positionInSegment);
          const lastPart = currentSegment.text.substring(positionInSegment);

          // Cập nhật segment hiện tại với phần đầu
          currentSegment.text = firstPart;

          // Thêm segment mới với văn bản được thêm vào và định dạng hiện tại
          newSegs.splice(targetSegmentIndex + 1, 0, {
            text: addedText,
            bold: format.bold,
            italic: format.italic,
            underline: format.underline,
          });

          // Thêm segment với phần cuối nếu có
          if (lastPart) {
            newSegs.splice(targetSegmentIndex + 2, 0, {
              ...currentSegment,
              text: lastPart,
            });
          }
        }

        // Hợp nhất các segment liền kề có cùng định dạng
        return mergeAdjacentSegments(newSegs);
      });

      // Cập nhật trạng thái
      if (newText.length > 0) {
        setLastInputChar(newText.charAt(newText.length - 1));
      } else {
        setLastInputChar('');
      }
      setIsComposingVietnamese(false);
    },
    [segments, format],
  );

  return {
    segments,
    setSegments,
    format,
    setFormat,
    selection,
    setSelection,
    handleSelectionChange,
    toggleFormat,
    onChangeText,
    lastInputChar,
    isComposingVietnamese,
  };
};
