import {StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    height: '100%',
    width: '100%',
  },
  editorContainer: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  scrollArea: {
    flex: 1,
  },
  placeholder: {
    position: 'absolute',
    top: 4,
    left: 0,
    color: '#8E8E93',
    fontSize: 14,
  },
  richText: {
    fontSize: 16,
    color: '#1c1c1e',
    lineHeight: 24,
    minHeight: 100, // Đảm bảo có đủ không gian cho văn bản
    paddingTop: 0,
    paddingBottom: 0,
  },
  hiddenInput: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    color: 'transparent',
    backgroundColor: 'transparent',
    fontSize: 16, // Phải giống với richText
    lineHeight: 24, // Phải giống với richText
    paddingTop: 0,
    paddingBottom: 0,
    textAlignVertical: 'top',
  },
  imagePreviewContainer: {
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    paddingTop: 8,
    paddingBottom: 8,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
  imagePreviewTitle: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginBottom: 8,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 8,
    borderRadius: 8,
    overflow: 'visible',
  },
  previewImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    zIndex: 10,
    padding: 4,
  },
  addMoreImagesButton: {
    width: 80,
    height: 80,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addMoreImagesText: {
    fontSize: 24,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  toolbar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderColor: '#E5E5EA',
    backgroundColor: '#fff',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  formatButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 16,
    width: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    fontSize: 18,
    color: '#666',
  },
  iconActive: {
    borderBottomWidth: 2,
    borderColor: '#000',
  },
  imagePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  imagePickerText: {
    ...TypoSkin.buttonText4,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginLeft: 4,
  },
});
