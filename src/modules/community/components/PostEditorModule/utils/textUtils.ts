import {TextSegment} from '../types';

// Chuyển đổi segments thành HTML
export const segmentsToHTML = (segments: TextSegment[]): string => {
  return segments
    .map(segment => {
      let text = segment.text;
      // Escape HTML characters
      text = text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');

      // Thêm các thẻ định dạng
      if (segment.bold) text = `<b>${text}</b>`;
      if (segment.italic) text = `<i>${text}</i>`;
      if (segment.underline) text = `<u>${text}</u>`;

      return text;
    })
    .join('');
};

// Hàm chuyển đổi HTML thành segments sử dụng regex
export const htmlToSegments = (html: string): TextSegment[] => {
  if (!html) return [{text: '', bold: false, italic: false, underline: false}];

  // Loại bỏ các thẻ không liên quan đến định dạng văn bản
  let cleanHtml = html
    .replace(/<(?!\/?(b|i|u|strong|em)\b)[^>]+>/gi, '')
    .replace(/\n/g, ' ')
    .trim();

  // Nếu không có HTML, trả về segment trống
  if (!cleanHtml) {
    return [{text: '', bold: false, italic: false, underline: false}];
  }

  const segments: TextSegment[] = [];
  let currentIndex = 0;

  // Regex để tìm các thẻ định dạng
  const tagRegex = /<\/?(?:b|i|u|strong|em)>/gi;
  let match;
  let lastIndex = 0;

  // Stack để theo dõi các thẻ đang mở
  const formatStack: {tag: string; index: number}[] = [];
  let currentFormat = {bold: false, italic: false, underline: false};

  // Tìm tất cả các thẻ trong HTML
  while ((match = tagRegex.exec(cleanHtml)) !== null) {
    const tag = match[0].toLowerCase();
    const isClosingTag = tag.startsWith('</');
    const tagName = isClosingTag ? tag.slice(2, -1) : tag.slice(1, -1);
    const normalizedTag =
      tagName === 'strong' ? 'b' : tagName === 'em' ? 'i' : tagName;

    // Xử lý text trước tag hiện tại
    if (match.index > lastIndex) {
      const text = cleanHtml.slice(lastIndex, match.index);
      // Giải mã các ký tự HTML đặc biệt
      const decodedText = text
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#039;/g, "'");

      if (decodedText) {
        segments.push({
          text: decodedText,
          bold: currentFormat.bold,
          italic: currentFormat.italic,
          underline: currentFormat.underline,
        });
      }
    }

    // Cập nhật định dạng hiện tại
    if (isClosingTag) {
      // Tìm thẻ mở tương ứng trong stack
      const openTagIndex = formatStack.findIndex(
        item => item.tag === normalizedTag,
      );
      if (openTagIndex !== -1) {
        // Xóa thẻ khỏi stack
        formatStack.splice(openTagIndex, 1);

        // Cập nhật định dạng
        switch (normalizedTag) {
          case 'b':
            currentFormat.bold = formatStack.some(item => item.tag === 'b');
            break;
          case 'i':
            currentFormat.italic = formatStack.some(item => item.tag === 'i');
            break;
          case 'u':
            currentFormat.underline = formatStack.some(
              item => item.tag === 'u',
            );
            break;
        }
      }
    } else {
      // Thêm thẻ mở vào stack
      formatStack.push({tag: normalizedTag, index: currentIndex});

      // Cập nhật định dạng
      switch (normalizedTag) {
        case 'b':
          currentFormat.bold = true;
          break;
        case 'i':
          currentFormat.italic = true;
          break;
        case 'u':
          currentFormat.underline = true;
          break;
      }
    }

    lastIndex = match.index + match[0].length;
    currentIndex++;
  }

  // Xử lý text còn lại sau tag cuối cùng
  if (lastIndex < cleanHtml.length) {
    const text = cleanHtml.slice(lastIndex);
    // Giải mã các ký tự HTML đặc biệt
    const decodedText = text
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#039;/g, "'");

    if (decodedText) {
      segments.push({
        text: decodedText,
        bold: currentFormat.bold,
        italic: currentFormat.italic,
        underline: currentFormat.underline,
      });
    }
  }

  // Nếu không có segments nào, trả về segment trống
  if (segments.length === 0) {
    return [{text: '', bold: false, italic: false, underline: false}];
  }

  return segments;
};

// Hàm hợp nhất các segment liền kề có cùng định dạng
export const mergeAdjacentSegments = (
  segments: TextSegment[],
): TextSegment[] => {
  if (segments.length <= 1) return segments;

  const result: TextSegment[] = [segments[0]];

  for (let i = 1; i < segments.length; i++) {
    const current = segments[i];
    const previous = result[result.length - 1];

    if (
      previous.bold === current.bold &&
      previous.italic === current.italic &&
      previous.underline === current.underline
    ) {
      // Nếu định dạng giống nhau, hợp nhất
      previous.text += current.text;
    } else {
      // Nếu định dạng khác nhau, thêm segment mới
      result.push(current);
    }
  }

  return result;
};

// Lấy full text từ segments
export const getFullText = (segments: TextSegment[]): string => {
  return segments.reduce((text, segment) => text + segment.text, '');
};
