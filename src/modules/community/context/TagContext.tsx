import React, {createContext, useContext, useState, ReactNode} from 'react';

interface TagContextType {
  selectedTag: any;
  setSelectedTag: (tag: any) => void;
  clearSelectedTag: () => void;
}

const TagContext = createContext<TagContextType | undefined>(undefined);

export const TagProvider: React.FC<{children: ReactNode}> = ({children}) => {
  const [selectedTag, setSelectedTag] = useState<any>(null);

  const clearSelectedTag = () => setSelectedTag(null);

  return (
    <TagContext.Provider value={{selectedTag, setSelectedTag, clearSelectedTag}}>
      {children}
    </TagContext.Provider>
  );
};

export const useTag = () => {
  const context = useContext(TagContext);
  if (context === undefined) {
    throw new Error('useTag must be used within a TagProvider');
  }
  return context;
};
