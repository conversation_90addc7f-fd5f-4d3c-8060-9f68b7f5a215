/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  RefreshControl,
} from 'react-native';
import {
  AppButton,
  ComponentStatus,
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  showDialog,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {DefaultPost, SkeletonPlacePostCard} from '../card/defaultPost';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {TypoSkin} from '../../../assets/skin/typography';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import {Ultis} from '../../../utils/Utils';
import {useTranslation} from 'react-i18next';
import {HomeHeader} from '../../../Screen/Layout/headers/HomeHeader';

export default function HomeCommunity() {
  const {t} = useTranslation();
  const user = useSelectorCustomerState().data;
  const [activeTab, setActiveTab] = React.useState(0);
  const bottomSheetRef = useRef<any>(null);

  const dialogRef = useRef<any>(null);
  const flatListRef = useRef<any>(null);
  const dispatch: AppDispatch = useDispatch();
  const navigation = useNavigation<any>();
  const size = 50;
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true); // Thêm state để kiểm tra còn data không
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const data = useSelector((state: RootState) => state.newsFeed.data);
  const {loading} = useSelector((state: RootState) => state.newsFeed);

  useEffect(() => {
    refreshData();
  }, [activeTab]);

  const handleRefresh = async () => {
    if (!loading) {
      // Thêm check này để tránh gọi refresh khi đang loading
      setIsRefreshing(true);
      setHasMore(true);
      try {
        refreshData();
      } catch (error) {
        console.error('Refresh error:', error);
      } finally {
      }
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  };

  const handleLoadMore = async () => {
    // Clear any existing timeout to prevent multiple calls
  };

  const refreshData = () => {
    if (activeTab === 0) {
      dispatch(newsFeedActions.getNewFeedPopular(1, size));
    } else if (activeTab === 1) {
      dispatch(newsFeedActions.getMyFeed(1, size, user?.Id));
    }
    // else if (activeTab === 2) {
    //   dispatch(newsFeedActions.getNewFeedFollowing(1, size));
    // } else if (activeTab === 3) {
    //   dispatch(newsFeedActions.getNewFeedSaved(1, size));
    // }
  };

  const TabBar = React.memo(() => {
    return (
      <View>
        <ScrollView
          showsHorizontalScrollIndicator={false}
          horizontal={true}
          contentContainerStyle={{
            gap: 8,
          }}
          style={styles.tabBar}>
          {/* write 3 tab in here */}
          {[
            {
              Id: 0,
              Name: t('community.tabs.popular'),
              Icon: 'outline/arrows/trend-up',
            },
            {
              Id: 1,
              Name: t('community.tabs.forYou'),
              Icon: 'outline/files/news',
            },
          ].map((tab, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => {
                setActiveTab(tab.Id);
                flatListRef.current?.scrollToOffset({
                  animated: false,
                  offset: 0,
                });
              }}
              style={[styles.tab, activeTab === tab.Id && styles.activeTab]}>
              <Winicon key={tab.Icon} src={tab.Icon} size={12} />
              <Text style={[styles.tabText]}>{tab.Name}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  });

  const PostItem = ({item, user}: {item: any; user: any}) => {
    return (
      <DefaultPost
        data={{
          ...item,
          relativeUser:
            item.CustomerId === user?.Id
              ? {
                  image: user?.AvatarUrl,
                  title: user?.Name,
                  subtitle: Ultis.getDiffrentTime(item.DateCreated),
                }
              : item.relativeUser,
        }}
        onPressDetail={() => navigate(RootScreen.PostDetail, {item: item})}
        actionView={
          <View
            style={{
              flexDirection: 'row',
              paddingTop: 16,
              alignItems: 'center',
              gap: 8,
            }}>
            <AppButton
              backgroundColor={ColorThemes.light.neutral_main_background_color}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              onPress={async () => {
                if (user) {
                  await dispatch(
                    newsFeedActions.updateLike(item.Id, item.IsLike === true),
                  );
                } else {
                  ///TODO: check chưa login thì confirm ra trang login
                  dialogCheckAcc(dialogRef);
                }
              }}
              title={
                <Text
                  style={{
                    ...TypoSkin.buttonText5,
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  }}>
                  {item.Likes ?? 0}
                </Text>
              }
              textColor={
                item.IsLike === true
                  ? ColorThemes.light.error_main_color
                  : ColorThemes.light.neutral_text_subtitle_color
              }
              prefixIconSize={12}
              prefixIcon={
                item.IsLike === true
                  ? 'fill/emoticons/heart'
                  : 'outline/emoticons/heart'
              }
            />
            <AppButton
              backgroundColor={ColorThemes.light.neutral_main_background_color}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              onPress={async () => {
                navigate(RootScreen.PostDetail, {item: item});
              }}
              prefixIcon={'outline/user interface/b-comment'}
              prefixIconSize={12}
              textColor={ColorThemes.light.neutral_text_subtitle_color}
              title={
                <Text
                  style={{
                    ...TypoSkin.buttonText5,
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  }}>
                  {item.Comment ?? 0}
                </Text>
              }
            />
            {/* <AppButton
              backgroundColor={ColorThemes.light.neutral_main_background_color}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              onPress={async () => {
                onShare({content: 'Hello world'});
              }}
              prefixIcon={'fill/arrows/social-sharing'}
              prefixIconSize={12}
              textColor={ColorThemes.light.neutral_text_subtitle_color}
            /> */}
          </View>
        }
        onPressHeader={() => {
          // if (item.GroupId) {
          //   navigate(RootScreen.GroupIndex, {Id: item.GroupId});
          // } else {
          if (item.CustomerId)
            navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
          // }
        }}
        trailingView={
          item.CustomerId === user.Id && (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 4,
              }}>
              <AppButton
                backgroundColor={
                  ColorThemes.light.neutral_main_background_color
                }
                borderColor="transparent"
                onPress={() => {
                  showBottomSheet({
                    ref: bottomSheetRef,
                    title: t('community.actions'),
                    suffixAction: <View />,
                    prefixAction: (
                      <TouchableOpacity
                        onPress={() => hideBottomSheet(bottomSheetRef)}
                        style={{padding: 6, alignItems: 'center'}}>
                        <Winicon
                          src="outline/layout/xmark"
                          size={20}
                          color={ColorThemes.light.neutral_text_body_color}
                        />
                      </TouchableOpacity>
                    ),
                    children: (
                      <View
                        style={{
                          gap: 8,
                          height: Dimensions.get('window').height / 4,
                          width: '100%',
                          backgroundColor:
                            ColorThemes.light.neutral_absolute_background_color,
                        }}>
                        {/* <ListTile
                        onPress={() => {
                          hideBottomSheet(bottomSheetRef);

                          showSnackbar({
                            message: t('community.featureInDevelopment'),
                            status: ComponentStatus.WARNING,
                          });
                        }}
                        title={t('community.reportPost')}
                        titleStyle={{...TypoSkin.body3}}
                      /> */}
                        {item.CustomerId === user.Id && (
                          <ListTile
                            onPress={() => {
                              hideBottomSheet(bottomSheetRef);

                              navigation.push(RootScreen.createPost, {
                                editPost: item,
                              });
                              // showSnackbar({
                              //   message: 'Chức năng đang được phát triển',
                              //   status: ComponentStatus.WARNING,
                              // });
                            }}
                            title={t('community.editPost')}
                            titleStyle={{...TypoSkin.body3}}
                          />
                        )}
                        {item.CustomerId === user.Id && (
                          <ListTile
                            onPress={() => {
                              hideBottomSheet(bottomSheetRef);
                              showDialog({
                                ref: dialogRef,
                                status: ComponentStatus.WARNING,
                                title: 'Bạn chắc chắn muốn xóa bài đăng này?',
                                onSubmit: async () => {
                                  dispatch(newsFeedActions.deletePost(item));
                                },
                              });
                            }}
                            title={t('community.deletePost')}
                            titleStyle={{...TypoSkin.body3}}
                          />
                        )}
                      </View>
                    ),
                  });
                }}
                containerStyle={{
                  borderRadius: 100,
                  padding: 6,
                  height: 24,
                  width: 24,
                }}
                title={
                  <Winicon
                    src={'fill/user interface/menu-dots'}
                    size={14}
                    color={ColorThemes.light.neutral_text_subtitle_color}
                  />
                }
              />
            </View>
          )
        }
        showContent={true}
      />
    );
  };
  const getItemLayout = React.useCallback(
    (data: any, index: number) => ({
      length: 300, // Approximate height of each item
      offset: 300 * index,
      index,
    }),
    [],
  );

  return (
    <View style={styles.container}>
      <FBottomSheet ref={bottomSheetRef} />
      <HomeHeader
        onSearchPress={() => {
          navigation.navigate(RootScreen.SearchCommunity, {type: 'community'});
        }}
      />

      {/*  */}
      <TabBar />

      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          padding: 8,
          paddingHorizontal: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
          borderTopWidth: 1,
          borderTopColor: ColorThemes.light.neutral_main_border_color,
          borderBottomWidth: 1,
          borderBottomColor: ColorThemes.light.neutral_main_border_color,
        }}
        onPress={() => {
          if (!user) {
            dialogCheckAcc(dialogRef);
            return;
          }
          navigation.push(RootScreen.createPost, {groupId: null});
        }}>
        <View
          style={{
            width: 30,
            height: 30,
            borderRadius: 20,
            backgroundColor: '#f0f0f0',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 8,
          }}>
          <Winicon
            src="outline/layout/plus"
            size={16}
            color={ColorThemes.light.neutral_text_title_color}
          />
        </View>
        <Text
          style={{
            ...TypoSkin.buttonText5,
            color: ColorThemes.light.neutral_text_title_color,
          }}>
          {t('community.postPlaceholder')}
        </Text>
      </TouchableOpacity>

      <FlatList
        ref={flatListRef}
        data={data}
        renderItem={({item}) => <PostItem item={item} user={user} />}
        keyExtractor={item => item?.Id.toString()}
        getItemLayout={getItemLayout}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        windowSize={10}
        initialNumToRender={5}
        showsVerticalScrollIndicator={false}
        style={{
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        contentContainerStyle={{
          gap: data?.length == 0 ? 0 : 8,
          backgroundColor: ColorThemes.light.neutral_main_background_color,
        }}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={() => {
          if (loading && data?.length === 0) {
            return (
              <View
                style={{
                  gap: 8,
                  backgroundColor:
                    ColorThemes.light.neutral_absolute_background_color,
                }}>
                {[1, 2, 3].map((_, index) => (
                  <SkeletonPlacePostCard key={`skeleton-${index}`} />
                ))}
              </View>
            );
          } else {
            return (
              <View
                style={{
                  flex: 1,
                  backgroundColor:
                    ColorThemes.light.neutral_absolute_background_color,
                }}>
                <EmptyPage title="Không có dữ liệu" />
              </View>
            );
          }
        }}
        ListFooterComponent={() => {
          if (loading && !isRefreshing) {
            return (
              <View
                style={{
                  backgroundColor:
                    ColorThemes.light.neutral_absolute_background_color,
                }}>
                <SkeletonPlacePostCard />
              </View>
            );
          }
          if (!hasMore && data?.length > 0) {
            return null;
          }
          return <View style={{height: 100}} />;
        }}
      />
      <FDialog ref={dialogRef} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 0,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  tabBar: {
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,
  },
  activeTab: {
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderWidth: 0,
  },
  tabText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginLeft: 4,
  },
  activeTabText: {
    color: '#333',
    fontWeight: 'bold',
  },
  postContainer: {
    backgroundColor: '#fff',
    marginBottom: 8,
  },
  defaultPostContainer: {
    paddingHorizontal: 0,
  },
  postHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bookmarkButton: {
    marginRight: 16,
  },
  postFooter: {
    flexDirection: 'row',
    paddingTop: 16,
    alignItems: 'center',
    gap: 8,
  },
  footerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  footerButtonText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
});
