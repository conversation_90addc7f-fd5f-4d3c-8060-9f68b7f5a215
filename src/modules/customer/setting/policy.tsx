import {Dimensions, View} from 'react-native';
import {WebView} from 'react-native-webview';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useState, useMemo, useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {regexGetVariables} from '../../../utils/Utils';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {DataController} from '../../../base/baseController';
import ScreenHeader from '../../../Screen/Layout/header';
import {FLoading, ListTile, Winicon} from 'wini-mobile-components';
import TitleWithBackAction from '../../../Screen/Layout/titleWithBackAction';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ColorThemes} from '../../../assets/skin/colors';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';
import RenderHTML from 'react-native-render-html';

const {width} = Dimensions.get('window');

export default function PolicyView() {
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  const dispatch = useDispatch<any>();
  const [step, setStep] = useState(0);
  const [policyData, setPolicyData] = useState<any>();
  const [data, setData] = useState<Array<any>>([]);
  const now = new Date();

  useEffect(() => {
    const policyController = new DataController('Policy');
    policyController.getListSimple({page: 1, size: 10}).then(res => {
      if (res.code === 200 && res.data.length) setData(res.data);
    });
  }, []);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <InforHeader
        title="Chính sách"
        onBack={() => (step === 1 ? setStep(0) : navigation.goBack())}
      />
      {step ? (
        <RenderHTML
          contentWidth={width}
          baseStyle={{
            fontSize: 16,
            lineHeight: 24,
            paddingHorizontal: 16,
            paddingTop: 16,
          }}
          source={{html: policyData?.Content}}
        />
      ) : (
        <View style={{flex: 1}}>
          {data
            .filter((e: any) => e.Url !== '/partner')
            .map((item: any, index: number) => (
              <ListTile
                key={index}
                title={item?.Name}
                onPress={() => {
                  setStep(1);
                  setPolicyData(data.find((e: any) => e.Id === item.Id));
                }}
                trailing={
                  <Winicon
                    src="fill/arrows/arrow-sm-right"
                    size={24}
                    color={ColorThemes.light.neutral_text_body_color}
                  />
                }
              />
            ))}
        </View>
      )}
    </View>
  );
}
