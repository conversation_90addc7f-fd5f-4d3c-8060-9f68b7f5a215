import {View} from 'react-native';
import React, {useCallback, useState} from 'react';
import ScrollableTabs from './scrollable/ScrollableTabs';
import TabNews from './components/TabNews';
import TabEvents from './components/TabEvents';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import {faRocket, faBullhorn, faBook} from '@fortawesome/free-solid-svg-icons';
import TabGuide from './components/TabGuide';
import {ColorThemes} from '../../assets/skin/colors';

const TABS_DATA = [
  {
    id: 'news',
    label: 'Tin mới',
    icon: faRocket,
  },
  {
    id: 'news-event',
    label: 'Tin sự kiện',
    icon: faBullhorn,
  },
  {
    id: 'guides',
    label: 'Hướng dẫn',
    icon: faBook,
  },
];

const NewsScreen = () => {
  const [tab, setTab] = useState<string>('news');
  const onChangeTab = useCallback((tab: string) => {
    setTab(tab);
  }, []);
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <InforHeader title={'Tin tức'} />

      <View style={{flex: 1}}>
        <View style={{marginHorizontal: 12, marginVertical: 12}}>
          <ScrollableTabs data={TABS_DATA} onChangeTab={onChangeTab} />
        </View>

        {tab === 'news' && <TabNews />}
        {tab === 'news-event' && <TabEvents />}
        {tab === 'guides' && <TabGuide />}
      </View>
    </View>
  );
};

export default NewsScreen;
