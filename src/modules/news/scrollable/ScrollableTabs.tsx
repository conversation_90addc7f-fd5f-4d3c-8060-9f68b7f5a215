import React, {useState} from 'react';
import {
  FlatList,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {IconProp} from '@fortawesome/fontawesome-svg-core';

import {ColorThemes} from '../../../assets/skin/colors';

interface TabItem {
  id: string;
  label: string;
  icon: IconProp;
}

interface Props {
  data: TabItem[];
  onChangeTab: (tabId: string) => void;
}

const ScrollableTabs = ({data, onChangeTab}: Props) => {
  const [activeTabId, setActiveTabId] = useState(
    data?.[0]?.id ? data[0].id : '',
  );

  const handleTabPress = (tabId: string) => {
    if (tabId === activeTabId) {
      return;
    }
    setActiveTabId(tabId);
    onChangeTab?.(tabId);
  };

  const renderTabItem = ({item}: {item: TabItem}) => {
    const isActive = item.id === activeTabId;
    const iconColor = isActive ? ColorThemes.light.primary_main_color : '#333';

    const tabContent = (
      <View style={styles.tabItem}>
        <FontAwesomeIcon
          icon={item.icon}
          size={16}
          color={iconColor}
          style={styles.tabIcon}
        />
        <Text style={[styles.tabLabel, isActive && styles.activeTabLabel]}>
          {item.label}
        </Text>
      </View>
    );

    return (
      <TouchableOpacity
        style={styles.touchableContainer}
        onPress={() => handleTabPress(item.id)}>
        {isActive ? (
          <LinearGradient
            colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
            start={{x: 0, y: 0.5}}
            end={{x: 1, y: 0.5}}
            style={styles.gradientContainer}>
            {tabContent}
          </LinearGradient>
        ) : (
          tabContent
        )}
      </TouchableOpacity>
    );
  };

  if (!Array.isArray(data) || data.length === 0) {
    return null;
  }

  return (
    <View>
      <FlatList<TabItem>
        data={data}
        renderItem={renderTabItem}
        keyExtractor={(item, index) =>
          item?.id ? `${item.id}_${index}` : `tab_${index}`
        }
        horizontal
        showsHorizontalScrollIndicator={false}
        removeClippedSubviews={Platform.OS === 'ios'}
        initialNumToRender={5}
        maxToRenderPerBatch={5}
        windowSize={10}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  touchableContainer: {
    borderRadius: 10,
    overflow: 'hidden', // Necessary for borderRadius to work on iOS
    ...(Platform.OS === 'ios' && {
      shadowColor: 'transparent',
      shadowOffset: {width: 0, height: 0},
      shadowOpacity: 0,
      shadowRadius: 0,
    }),
  },
  gradientContainer: {
    borderRadius: 10,
    overflow: 'hidden', // Ensures the gradient respects the borderRadius on iOS
    ...(Platform.OS === 'ios' && {
      backgroundColor: 'transparent',
    }),
  },
  tabItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 10,
  },
  tabIcon: {
    marginRight: 8,
  },
  tabLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: ColorThemes.light.neutral_absolute_text_color,
  },
  activeTabLabel: {
    color: ColorThemes.light.primary_main_color,
  },
});

export default ScrollableTabs;
