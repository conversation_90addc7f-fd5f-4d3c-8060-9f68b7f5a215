import React, {useEffect, useState} from 'react';
import {View, Text, FlatList, StyleSheet, TouchableOpacity} from 'react-native';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import HotNewsCard from '../card/HotNewsCard';
import {NewsItem} from '../../../redux/models/news';
import {newsAction} from '../../../redux/actions/newsAction';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../router/router';

interface HotNewsSectionProps {
  title?: string;
  isRefresh?: boolean;
}

const HotNewsSection: React.FC<HotNewsSectionProps> = ({
  title = 'Tin tức hot',
  isRefresh = false,
}) => {
  const navigation = useNavigation<any>();
  const [hotNews, setHotNews] = useState<NewsItem[]>([]);

  useEffect(() => {
    initData();
  }, [isRefresh]);

  const initData = async () => {
    const response = await newsAction.fetch({
      page: 1,
      size: 5,
      sortby: [{prop: 'Views', direction: 'DESC'}],
    });
    if (response.data.length > 0) setHotNews(response.data);
  };

  // xem thêm
  const onSeeMore = () => {
    navigation.navigate(RootScreen.NewsScreen);
  };

  // chuyến đến trang chi tiết
  const onNavigateToDetail = (item: NewsItem) => {
    navigation.navigate(RootScreen.DetailNews, {id: item.Id});
  };

  // Không hiển thị gì nếu không có dữ liệu
  if (!hotNews || hotNews.length === 0) {
    return null;
  }

  return (
    <View style={styles.sectionContainer}>
      {/* Header */}
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        {
          <TouchableOpacity onPress={onSeeMore}>
            <Text style={styles.seeMoreButton}>Xem thêm</Text>
          </TouchableOpacity>
        }
      </View>

      {/* News List */}
      <FlatList
        data={hotNews}
        renderItem={({item}) => (
          <HotNewsCard item={item} onPress={() => onNavigateToDetail(item)} />
        )}
        keyExtractor={item => item.Id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContentContainer}
      />
    </View>
  );
};

export default HotNewsSection;

// ====== STYLES ======
const styles = StyleSheet.create({
  sectionContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    marginBottom: 12,
  },
  sectionTitle: {
    ...TypoSkin.title2,
    fontWeight: 'bold',
    color: ColorThemes.light.infor_text_color,
  },
  seeMoreButton: {
    fontSize: 16,
    color: '#007bff',
  },
  listContentContainer: {
    paddingLeft: 15,
  },
});
