import React, { useEffect, useState } from 'react';
import { View, RefreshControl } from 'react-native';
import { FlatList } from 'react-native';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';
import {
  MissionType,
  StatusOrder,
  Title,
  TransactionStatus,
  TransactionType,
} from '../../../Config/Contanst';
import CardOrder from '../card/CardOrder';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { OrderActions } from '../../../redux/reducers/OrderReducer';
import { useSelectorOrderState } from '../../../redux/hook/orderHook ';
import { DataController } from '../../../base/baseController';
import EmptyPage from '../../../Screen/emptyPage';
import { Ultis } from '../../../utils/Utils';
import { ColorThemes } from '../../../assets/skin/colors';
import { RootScreen } from '../../../router/router';
import { useSelectorShopState } from '../../../redux/hook/shopHook ';
import WalletDA from '../../wallet/da';
import store from '../../../redux/store/store';

interface OrderCardProps {
  type: string;
  setTypeCard: (type: string) => void;
  dataSearchResult: any[];
  dataSearch: string;
}
const ListCard = (props: OrderCardProps) => {
  const { type, dataSearchResult, dataSearch } = props;
  const navigation = useNavigation<any>();
  const [data, setData] = useState<any[]>();
  const [action, setAction] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const orderInfo = useSelectorOrderState().data;
  const shopController = new DataController('Order');
  const orderDetailController = new DataController('OrderDetail');
  const productController = new DataController('Product');
  const dispatch = useDispatch<any>();
  const shopInfo = useSelectorShopState().data;
  //refresh
  const [refreshing, setRefreshing] = useState(false);
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      if (shopInfo && shopInfo[0]?.Id) {
        await dispatch(OrderActions.getInforOrder(shopInfo[0].Id));
      } else {
        showSnackbar({
          message: 'Không thể làm mới dữ liệu. Vui lòng thử lại sau.',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      showSnackbar({
        message: 'Có lỗi xảy ra khi làm mới dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (type === Title.New) {
      setAction('Xác nhận đơn');
      setData(orderInfo?.NewOrder?.data);
    } else if (type === Title.Processing) {
      setAction('Cập nhật trạng thái');
      setData(orderInfo?.ProcessOrder?.data);
    } else if (type === Title.Cancel) {
      setAction(type);
      setData(orderInfo?.CancelOrder?.data);
    } else if (type === Title.Done) {
      setData(orderInfo?.DoneOrder?.data);
    }
  }, [type, action, orderInfo]);

  const handleUpdateStatusProcessOrder = async (item: any, type?: string) => {
    try {
      setLoading(true);
      let order = item;

      if (type == 'processing') {
        // Lấy chi tiết đơn hàng
        const orderDetailResponse = await orderDetailController.getListSimple({
          query: `@OrderId: {${order?.Id}}`,
        });

        if (
          orderDetailResponse?.code === 200 &&
          orderDetailResponse?.data?.length > 0
        ) {
          const orderDetails = orderDetailResponse.data;

          for (const detail of orderDetails) {
            const productId = detail?.ProductId;
            const orderQuantity = detail?.Quantity || 1;

            if (productId) {
              const productResponse = await productController.getListSimple({
                query: `@Id:{${productId}}`,
              });

              if (
                productResponse?.code === 200 &&
                productResponse?.data?.length > 0
              ) {
                const product = productResponse.data[0];
                const currentStock = product?.InStock || 0;

                if (currentStock <= 0) {
                  showSnackbar({
                    message: `Sản phẩm "${product?.Name}" đã hết hàng. Không thể xử lý đơn hàng này.`,
                    status: ComponentStatus.ERROR,
                  });
                  setLoading(false); // Stop loading here to allow user to retry or cancel
                  return; // Stop processing this order
                }

                if (orderQuantity > currentStock) {
                  showSnackbar({
                    message: `Sản phẩm "${product?.Name}" chỉ còn ${currentStock} sản phẩm trong kho. Đơn hàng yêu cầu ${orderQuantity} sản phẩm.`,
                    status: ComponentStatus.ERROR,
                  });
                  setLoading(false); // Stop loading here to allow user to retry or cancel
                  return; // Stop processing this order
                }

                // Cập nhật số lượng tồn kho của sản phẩm
                const newStock = currentStock - orderQuantity;
                const updatedProduct = {
                  ...product,
                  InStock: newStock,
                  Status: newStock === 0 ? 2 : 1,
                };

                const productUpdateResponse = await productController.edit([
                  updatedProduct,
                ]);

                if (productUpdateResponse?.code !== 200) {
                  showSnackbar({
                    message:
                      'Có lỗi xảy ra khi cập nhật số lượng tồn kho sản phẩm',
                    status: ComponentStatus.ERROR,
                  });
                  setLoading(false);
                  return;
                }
              } else {
                showSnackbar({
                  message: `Không tìm thấy thông tin sản phẩm với ID: ${productId}`,
                  status: ComponentStatus.ERROR,
                });
                setLoading(false);
                return;
              }
            }
          }

          // Cập nhật trạng thái đơn hàng (chỉ khi tất cả sản phẩm được xử lý thành công)
          const res = await shopController.edit([
            {
              Id: order.Id,
              CustomerId: order.CustomerId,
              ShopId: order.ShopId,
              Code: order.Code,
              Status: StatusOrder.proccess,
              DateProcess: new Date().getTime(),
            },
          ]);
          console.log('check-res', res);

          if (res.code == 200) {
            showSnackbar({
              message:
                'Cập nhật trạng thái đơn hàng sang trạng thái đang xử lý thành công',
              status: ComponentStatus.SUCCSESS,
            });
            dispatch(OrderActions.getInforOrder(shopInfo[0].Id));
          }
          navigation.navigate(RootScreen.OrderDetail, {
            type: Title.Processing,
            status: 2,
          });
        } else {
          showSnackbar({
            message: 'Không tìm thấy chi tiết đơn hàng. Không thể xử lý.',
            status: ComponentStatus.ERROR,
          });
          setLoading(false);
          return;
        }
      }

      if (type == 'completed') {
        // Cập nhật trạng thái đơn hàng
        const res = await shopController.edit([
          {
            Id: order.Id,
            CustomerId: order.CustomerId,
            ShopId: order.ShopId,
            Code: order.Code,
            Status: StatusOrder.success,
            DateCompleted: new Date().getTime(),
          },
        ]);
        if (res.code == 200) {
          //#region cập nhật order detail khi đơn hàng hoàn thành
          const orderDetailResponse = await orderDetailController.getListSimple(
            {
              query: `@OrderId: {${order?.Id}}`,
            },
          );

          if (
            orderDetailResponse?.code === 200 &&
            orderDetailResponse?.data?.length > 0
          ) {
            const orderDetails = orderDetailResponse.data;
            // Cập nhật trạng thái của các order detail
            const updatedOrderDetails = orderDetails.map((detail: any) => ({
              Id: detail.Id,
              Status: 3,
            }));

            const orderDetailUpdateResponse = await orderDetailController.edit(
              updatedOrderDetails,
            );

            if (orderDetailUpdateResponse?.code !== 200) {
              console.error(
                'Error updating order detail status:',
                orderDetailUpdateResponse,
              );
              showSnackbar({
                message:
                  'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi cập nhật trạng thái chi tiết đơn hàng',
                status: ComponentStatus.WARNING,
              });
            }

            // Cập nhật số lượng sản phẩm đã bán cho từng sản phẩm trong đơn hàng
            for (const detail of orderDetails) {
              const productId = detail?.ProductId;
              const orderQuantity = detail?.Quantity || 1;

              if (productId) {
                try {
                  // Lấy thông tin sản phẩm hiện tại
                  const productResponse = await productController.getListSimple(
                    {
                      query: `@Id:{${productId}}`,
                    },
                  );

                  if (
                    productResponse?.code === 200 &&
                    productResponse?.data?.length > 0
                  ) {
                    const product = productResponse.data[0];
                    const currentSold = product?.Sold || 0;

                    // Cập nhật số lượng đã bán
                    const newSold = currentSold + orderQuantity;
                    const updatedProduct = {
                      ...product,
                      Sold: newSold,
                    };

                    const productUpdateResponse = await productController.edit([
                      updatedProduct,
                    ]);

                    if (productUpdateResponse?.code !== 200) {
                      console.error(
                        'Error updating product sold count:',
                        productUpdateResponse,
                      );
                      showSnackbar({
                        message:
                          'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi cập nhật số lượng đã bán',
                        status: ComponentStatus.WARNING,
                      });
                    }
                  }
                } catch (error) {
                  console.error('Error updating product sold count:', error);
                  showSnackbar({
                    message:
                      'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi cập nhật số lượng đã bán',
                    status: ComponentStatus.WARNING,
                  });
                }
              }
            }
          } else {
            showSnackbar({
              message:
                'Không tìm thấy chi tiết đơn hàng khi hoàn thành. Không thể cập nhật số lượng đã bán.',
              status: ComponentStatus.WARNING,
            });
          }
          //#endregion
          // #region update mission customer khi tổng đơn hàng trong tháng đạt yêu cầu
          //lấy ngày bắt đẩu và kết thúc của tháng
          const { start, end } = Ultis.getMonthRange();
          const [orderRes, missionCustomer] = await Promise.all([
            new DataController('Order').group({
              reducers:
                'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS Total',
              searchRaw: `@CustomerId: {${order.CustomerId}} @DateCreated: [${start} ${end}] @Status: [${StatusOrder.success}]`,
            }),
            new DataController('Mission').getListSimple({
              page: 1,
              size: 1,
              query: `@MissonType: [${MissionType.month}] @Type: [2]`, // nhiệm vụ tháng
            }),
          ]);
          if (orderRes.code === 200 && missionCustomer.code === 200) {
            const total = orderRes.data.reduce(
              (total: number, item: any) => total + parseFloat(item.Total),
              0,
            );
            if (total >= missionCustomer.data[0]?.TotalPriceOrder) { 
              const walletDA = new WalletDA();
              const rankInfo = store.getState().customer.rankInfo;
              await walletDA.CaculateMisson(
                order.CustomerId,
                MissionType.month,
                rankInfo,
              );             
              //update tất cả bản ghi trong tháng hiện tại của history reward
              const historyRewardController = new DataController(
                'HistoryReward',
              );
              const historyReward = await historyRewardController.getListSimple(
                {
                  query: `@CustomerId: {${order.CustomerId}} @DateCreated: [${start} ${end}] @Type: [${TransactionType.mission}] @Status: [${TransactionStatus.pending}]`,
                },
              );
              if (historyReward.code === 200) {
                const updatedHistoryRewards = historyReward.data.map(
                  (item: any) => ({
                    ...item,
                    Status: TransactionStatus.success,
                  }),
                );
                await historyRewardController.edit(updatedHistoryRewards);
              }
            }
          }
          // #endregion

          showSnackbar({
            message:
              'Cập nhật trạng thái đơn hàng sang trạng thái đã hoàn thành',
            status: ComponentStatus.SUCCSESS,
          });
          dispatch(OrderActions.getInforOrder(shopInfo[0].Id));

          // Chuyển trang sau khi cập nhật thành công
          navigation.navigate(RootScreen.OrderDetail, {
            type: Title.Done,
            status: 3,
          });
        }
      }

      if (type == 'cancelled') {
        // Cập nhật trạng thái đơn hàng
        const res = await shopController.edit([
          {
            Id: order.Id,
            CustomerId: order.CustomerId,
            ShopId: order.ShopId,
            Code: order.Code,
            Status: StatusOrder.cancel,
            DateUpdate: new Date().getTime(),
            CancelReason: order.cancelReason || '',
          },
        ]);
        if (res.code == 200) {
          // Khôi phục số lượng tồn kho của sản phẩm khi hủy đơn hàng
          const orderDetailResponse = await orderDetailController.getListSimple(
            {
              query: `@OrderId: {${order?.Id}}`,
            },
          );

          if (
            orderDetailResponse?.code === 200 &&
            orderDetailResponse?.data?.length > 0
          ) {
            const orderDetails = orderDetailResponse.data;

            for (const detail of orderDetails) {
              const productId = detail?.ProductId;
              const orderQuantity = detail?.Quantity || 1;

              if (productId) {
                try {
                  // Lấy thông tin sản phẩm hiện tại
                  const productResponse = await productController.getListSimple(
                    {
                      query: `@Id:{${productId}}`,
                    },
                  );

                  if (
                    productResponse?.code === 200 &&
                    productResponse?.data?.length > 0
                  ) {
                    const product = productResponse.data[0];
                    const currentStock = product?.InStock || 0;

                    // Khôi phục số lượng tồn kho bằng cách cộng thêm số lượng đã đặt hàng
                    const newStock = currentStock + orderQuantity;
                    const updatedProduct = {
                      ...product,
                      InStock: newStock,
                      Status: newStock === 0 ? 2 : 1,
                    };

                    const productUpdateResponse = await productController.edit([
                      updatedProduct,
                    ]);

                    if (productUpdateResponse?.code !== 200) {
                      console.error(
                        'Error updating product stock when cancelling order:',
                        productUpdateResponse,
                      );
                      showSnackbar({
                        message:
                          'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi khôi phục số lượng tồn kho sản phẩm',
                        status: ComponentStatus.WARNING,
                      });
                    } else {
                      console.log(
                        `Đã khôi phục ${orderQuantity} sản phẩm cho sản phẩm ${product?.Name}`,
                      );
                    }
                  } else {
                    showSnackbar({
                      message: `Không tìm thấy thông tin sản phẩm với ID: ${productId} khi hủy đơn hàng.`,
                      status: ComponentStatus.WARNING,
                    });
                  }
                } catch (error) {
                  console.error(
                    'Error updating product stock when cancelling order:',
                    error,
                  );
                  showSnackbar({
                    message:
                      'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi khôi phục số lượng tồn kho sản phẩm',
                    status: ComponentStatus.WARNING,
                  });
                }
              }
            }
          } else {
            showSnackbar({
              message:
                'Không tìm thấy chi tiết đơn hàng khi hủy. Không thể khôi phục số lượng tồn kho.',
              status: ComponentStatus.WARNING,
            });
          }

          showSnackbar({
            message: 'Cập nhật trạng thái đơn hàng sang trạng thái hủy',
            status: ComponentStatus.SUCCSESS,
          });
          dispatch(OrderActions.getInforOrder(shopInfo[0].Id));

          navigation.navigate(RootScreen.OrderDetail, {
            type: Title.Cancel,
            status: 4,
          });
        }
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi cập nhật trạng thái đơn hàng',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };
  const handleViewDetailOrder = (item: any, refundInfo: any) => {
    navigation.push(RootScreen.OrderDetailPageForShop, {
      orderId: item.Id,
      type: 'Shop',
      CancelReason: item.CancelReason,
      refundInfo: refundInfo,
    });
  };

  if (
    (data && data?.length > 0) ||
    (dataSearchResult && dataSearchResult.length > 0)
  ) {
    return (
      <FlatList
        data={dataSearch ? dataSearchResult : data}
        style={{ flex: 1 }}
        keyExtractor={(item, i) => `${i} ${item.id}`}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        renderItem={({ item, index }) => (
          <CardOrder
            item={item}
            index={index}
            action={action}
            handleUpdateStatusProcessOrder={handleUpdateStatusProcessOrder}
            handleViewDetailOrder={handleViewDetailOrder}
          />
        )}
      />
    );
  } else {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          width: '100%',
        }}>
        <EmptyPage />
      </View>
    );
  }
};

export default ListCard;
