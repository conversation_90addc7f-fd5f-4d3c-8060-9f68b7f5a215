import {de} from 'date-fns/locale';
import {DataController} from '../../base/baseController';
import {TransactionType} from '../../Config/Contanst';

export class OrderDA {
  private orderController: DataController;
  private orderDetailController: DataController;
  private productController: DataController;
  private historyRewardController: DataController;
  private rewardController: DataController;
  private shopRewardController: DataController;
  private shopCateController: DataController;

  constructor() {
    this.orderController = new DataController('Order');
    this.orderDetailController = new DataController('OrderDetail');
    this.productController = new DataController('Product');
    this.historyRewardController = new DataController('HistoryReward');
    this.rewardController = new DataController('Reward');
    this.shopRewardController = new DataController('ShopReward');
    this.shopCateController = new DataController('ShopCategory');
  }

  async getAllHistoryReward() {
    try {
      const response = await this.historyRewardController.getListSimple({
        query: `@Filial:[0 1 2]`,
      });

      if (response?.code === 200) {
        return response;
      }

      // Log error for debugging
      console.error('getAllHistoryReward failed:', response);
      return {
        code: response?.code || 500,
        data: [],
        message: response?.message || 'Failed to get history rewards',
      };
    } catch (error) {
      console.error('getAllHistoryReward error:', error);
      return {
        code: 500,
        data: [],
        message: 'Internal server error',
      };
    }
  }

  async createOrder(order: any) {
    const response = await this.orderController.add(order);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async createOrderDetail(orderDetail: any) {
    const response = await this.orderDetailController.add(orderDetail);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  // async getOrderByShopId(ShopId: string, status: number) {
  //   // const response = await this.orderController.getPatternList({
  //   //   query: `@ShopId: {${ShopId}} @Status:[${status}]`,
  //   //   pattern: {
  //   //     ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
  //   //   },
  //   // });
  //   // if (response?.code === 200) {
  //   //   console.log('check-response-order', response?.data);
  //   //   let arrayData: any[] = [];
  //   //   for (let item of response?.data) {
  //   //     arrayData.push(item?.Id);
  //   //   }
  //   //   if (arrayData?.length > 0) {
  //   //     console.log('check-arrayData', arrayData);
  //   //     let response = await this.orderDetailController.getListSimple({
  //   //       query: `@OrderId: {${arrayData.join('|')}}`,
  //   //     });
  //   //     console.log('check-response-detail', response);
  //   //     if (response?.code === 200) {
  //   //       return response;
  //   //     }
  //   //   }
  //   //   return {
  //   //     code: 200,
  //   //     data: [],
  //   //     message: 'No orders found',
  //   //   };
  //   // }
  //   // return {
  //   //   code: 200,
  //   //   data: [],
  //   //   message: 'No orders found',
  //   // };
  async getOrderByShopId(ShopId: string, status: number) {
    try {
      // Bước 1: Lấy tất cả orders từ bảng Order theo ShopId và status
      const orderResponse = await this.orderController.getPatternList({
        query: `@ShopId: {${ShopId}} @Status:[${status} ${status}]`,
        pattern: {
          Id: [
            'Id',
            'Status',
            'CustomerId',
            'ShopId',
            'AddressId',
            'TotalAmount',
            'CreatedDate',
          ],
        },
      });

      if (
        orderResponse?.code !== 200 ||
        !orderResponse.data ||
        orderResponse.data.length === 0
      ) {
        return {
          code: 200,
          data: [],
          message: 'No orders found',
        };
      }

      // Lấy tất cả OrderId từ response
      const orderIds = orderResponse.data.map((order: any) => order.Id);

      // Bước 2: Lấy tất cả orderDetails theo OrderId
      const orderDetailResponse =
        await this.orderDetailController.getPatternList({
          query: `@OrderId: {${orderIds.join('|')}}`,
          pattern: {
            ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
          },
        });

      if (orderDetailResponse?.code !== 200) {
        return {
          code: 200,
          data: [],
          message: 'Failed to get order details',
        };
      }

      // Bước 3: Map thông tin từ cả hai bảng
      const result = orderDetailResponse.data.map((orderDetail: any) => {
        // Tìm thông tin order tương ứng
        const orderInfo = orderResponse.data.find(
          (order: any) => order.Id === orderDetail.OrderId,
        );

        // Gắn thông tin order vào orderDetail
        if (orderInfo) {
          orderDetail.orderInfo = orderInfo;
        }

        return orderDetail;
      });

      return {
        code: 200,
        data: result,
        message: 'Success',
      };
    } catch (error) {
      console.error('getOrderByShopId error:', error);
      return {
        code: 500,
        data: [],
        message: 'Internal server error',
      };
    }
  }

  async getAllOrdersByShopId(ShopId: string) {
    // Bước 1: Lấy tất cả orders từ bảng Order theo ShopId
    const orderResponse = await this.orderController.getListSimple({
      query: `@ShopId: {${ShopId}}`,
    });

    if (
      orderResponse?.code !== 200 ||
      !orderResponse.data ||
      orderResponse.data.length === 0
    ) {
      return {
        code: 200,
        data: [],
        message: 'No orders found',
      };
    }
    // Lấy tất cả OrderId từ response
    const orderIds = orderResponse.data.map((order: any) => order.Id);
    // Bước 2: Gọi 1 lần API để lấy tất cả orderDetails
    const orderDetailResponse = await this.orderDetailController.getPatternList(
      {
        query: `@OrderId: {${orderIds.join('|')}}`,
        pattern: {
          ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
        },
      },
    );

    if (orderDetailResponse?.code !== 200) {
      return {
        code: 200,
        data: [],
        message: 'Failed to get order details',
      };
    }
    //map product vào orderdetail
    orderDetailResponse.data = orderDetailResponse.data.map((item: any) => {
      item.productInfo = orderDetailResponse.Product.find(
        (product: any) => product.Id === item.ProductId,
      );
      return item;
    });
    // Bước 5: Gọi 1 lần API để lấy tất cả historyReward với điều kiện Filial là 0, 1, 2
    const orderDetailIds = orderDetailResponse.data.map(
      (orderDetail: any) => orderDetail.Id,
    );
    const historyRewardResponse =
      await this.historyRewardController.getListSimple({
        query: `@OrderDetailId: {${orderDetailIds.join('|')}}`,
      });

    if (historyRewardResponse?.code !== 200) {
      return {
        code: 200,
        data: [],
        message: 'Failed to get history reward',
      };
    }
    //map historyReward vào orderdetail
    orderDetailResponse.data = orderDetailResponse.data.map((item: any) => {
      item.historyReward = historyRewardResponse.data.filter(
        (history: any) => history.OrderDetailId === item.Id,
      );
      return item;
    });
    // Bước 7: Map tất cả thông tin lại với nhau
    const result = orderResponse.data.map((order: any) => {
      order.orderDetails = orderDetailResponse.data.filter(
        (orderDetail: any) => orderDetail.OrderId === order.Id,
      );
      return order;
    });
    return {
      code: 200,
      data: result,
      message: 'Success',
    };
  }

  async getOrderByCustomerId(CustomerId: string) {
    const response = await this.orderController.getListSimple({
      query: `@CustomerId: {${CustomerId}}`,
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }
  async getOrderByOrderId(OrderId: string) {
    const patternResponse = await this.orderController.getPatternList({
      query: `@Id: {${OrderId}}`,
      pattern: {
        CustomerId: ['Id', 'Name', 'Mobile', 'Email', 'AvatarUrl'],
        ShopId: ['Id', 'Name', 'Avatar'],
        AddressId: ['Id', 'Address', 'Name', 'Mobile', 'Email'],
      },
    });
    return patternResponse;
  }
  async getOrderDetailsByOrderId(OrderId: string) {
    const response = await this.orderDetailController.getPatternList({
      query: `@OrderId: {${OrderId}}`,
      pattern: {
        ProductId: [
          'Id',
          'Name',
          'Img',
          'Description',
          'Price',
          'ShopId',
          'CategoryId',
          'IsFreeShip',
          'Discount',
        ],
      },
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getMoneyDetailsByOrderDetailId(OrderDetailId: string) {
    const response = await this.historyRewardController.getPatternList({
      query: `@OrderDetailId: {${OrderDetailId}} @Type :[${TransactionType.hoahong}]`,
      pattern: {
        OrderDetailId: ['Id', 'Money'],
      },
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }
  async getMoneyDetailsByListOrderDetailId(OrderDetailId: string[]) {
    const response = await this.historyRewardController.getListSimple({
      query: `@OrderDetailId: {${OrderDetailId.join('|')}}`,
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getInfoProductById(productId: string) {
    const response = await this.productController.getListSimple({
      query: `@Id: {${productId}}`,
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async updateProduct(data: any) {
    // Đảm bảo data là array cho productController.edit
    const dataArray = Array.isArray(data) ? data : [data];
    const response = await this.productController.edit(dataArray);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }
  async updateOrder(data: any) {
    const response = await this.orderController.edit(data);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  // async getOrderWithDetails(OrderId: string) {
  //   try {
  //     // Lấy thông tin đơn hàng
  //     const orderResponse = await this.getOrderByOrderId(OrderId);
  //     if (orderResponse?.code !== 200 || !orderResponse.data?.length) {
  //       return null;
  //     }

  //     const order = orderResponse.data[0];

  //     // Lấy chi tiết đơn hàng
  //     const detailsResponse = await this.getOrderDetailsByOrderId(OrderId);
  //     if (detailsResponse?.code === 200 && detailsResponse.data?.length) {
  //       // Lấy thông tin sản phẩm cho mỗi chi tiết đơn hàng
  //       const details = detailsResponse.data;
  //       const productIds = details.map(detail => detail.ProductId);

  //       // Lấy thông tin sản phẩm
  //       if (productIds.length > 0) {
  //         const productsQuery = productIds.map(id => `{${id}}`).join(' | ');
  //         const productsResponse = await this.productController.getListSimple({
  //           query: `@Id: ${productsQuery}`,
  //         });

  //         if (productsResponse?.code === 200 && productsResponse.data?.length) {
  //           const products = productsResponse.data;

  //           // Gắn thông tin sản phẩm vào chi tiết đơn hàng
  //           details.forEach(detail => {
  //             const product = products.find(p => p.Id === detail.ProductId);
  //             if (product) {
  //               detail.Product = product;
  //             }
  //           });
  //         }
  //       }

  //       order.details = details;
  //     }

  //     return order;
  //   } catch (error) {
  //     console.error('Error getting order with details:', error);
  //     return null;
  //   }
  // }
  //update trạng thái
  async updateStatusOrder(OrderId: string, Status: number) {
    console.log('updateStatusOrder - OrderId:', OrderId, 'Status:', Status);
    //lấy order
    const order = await this.getOrderByOrderId(OrderId);
    console.log('updateStatusOrder - order response:', order);
    if (order?.code === 200 && order?.data?.length > 0) {
      const updatedOrder = {...order?.data[0], Status: Status};
      console.log('updateStatusOrder - updatedOrder:', updatedOrder);
      const response = await this.orderController.edit([updatedOrder]);
      console.log('updateStatusOrder - edit response:', response);
      if (response?.code === 200) {
        return response;
      } else {
        console.log('updateStatusOrder - edit failed:', response);
      }
    } else {
      console.log('updateStatusOrder - failed to get order or no data');
    }
    return null;
  }

  async cancelOrder(order: any) {
    const response = await this.orderController.edit([order]);
    if (response?.code === 200) {
      return response;
    }
  }
}
