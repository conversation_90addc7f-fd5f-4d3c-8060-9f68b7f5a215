import { StyleSheet, Dimensions } from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';


const { width } = Dimensions.get('window');

// Constants
export const ITEM_WIDTH = width * 0.32;
export const ITEM_SPACING = 10;

export const OrderDetailStyles = StyleSheet.create({
    container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
  orderInfo: {
    display: 'flex',
    marginLeft: 13,
  },
  title: {
    ...TypoSkin.title3,
    fontWeight: '500',
    fontFamily: 'roboto',
  },
  numberOrder: {
    ...TypoSkin.title4,
    marginTop: 10,
    color: ColorThemes.light.neutral_text_placeholder_color,
    marginBottom: 8,
  },
});
