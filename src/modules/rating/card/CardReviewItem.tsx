import React from 'react';
import {
  View,
  Image,
  Text,
  StyleSheet,
  Pressable,
  ScrollView,
} from 'react-native';
import { AppSvg } from 'wini-mobile-components';
import { TypeMenuReview } from '../../../Config/Contanst';
import ReviewInfo from '../../../modules/rating/component/ReviewInfo';
import { TypoSkin } from '../../../assets/skin/typography';
import iconSvg from '../../../svg/icon';

const ReviewProductIteCard = (
  { item, index }: { item: any; index: number },
  type: string,
  isLoading: boolean,
) => {
  return (
    <Pressable style={styles.wrapper} key={`key ${index}`}>
      <View style={styles.review}>
        <ReviewInfo item={item} index={index} />
        <View style={styles.reviewContent}>
          {type == TypeMenuReview.Product && (
            <Text style={styles.description}>{item.Description}</Text>
          )}
          {type == TypeMenuReview.Product ? (
            <ScrollView horizontal={true}>
              {item?.ListImg?.length > 0 &&
                item?.ListImg?.split(',')?.map(
                  (image: string, index: number) => (
                    <Image
                      key={`item-${index}`}
                      source={{ uri: image }}
                      style={styles.avatarProduct}
                    />
                  ),
                )}
            </ScrollView>
          ) : (
            <View></View>
          )}

          {type == TypeMenuReview.Product ? (
            <View style={styles.reviewDetail}>
              <View style={styles.imageDetail}>
                <Image
                  source={{
                    uri: item?.Product?.Img,
                  }}
                  style={styles.avatarDetail}
                />
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  marginLeft: 11,
                  maxWidth: '100%',
                }}>
                <Text style={styles.tag}>{item?.Product?.Name}</Text>
                <Text style={styles.size}>{item?.Product?.Description}</Text>
                <Text style={styles.size}>
                  Hoàn tiền: {item?.product?.Refund ? item?.product?.Refund : 0}{' '}
                  CANPOINT
                </Text>
              </View>
            </View>
          ) : (
            <View style={styles.reviewDetail}>
              <View style={styles.orderDetail}>
                <AppSvg SvgSrc={iconSvg.orderReviewAction} size={24} />
                <Text style={styles.orderName}>{item?.OrderId}</Text>
              </View>
            </View>
          )}
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginTop: 26,
    marginRight: 20,
    marginLeft: 25,
  },

  review: {
    flexDirection: 'column',
    borderBottomWidth: 0.2,
    borderBottomColor: '#00FFFF',
    width: '100%',
  },

  reviewContent: {
    marginLeft: '19%',
  },

  description: {
    ...TypoSkin.body3,
    marginVertical: 5,
  },
  imagesProduct: {
    marginTop: 10,
    height: 100,
    width: '100%',
  },
  images: {
    flexDirection: 'row',
    marginTop: 10,
  },
  productImage: {
    width: 100,
    height: 100,
    marginRight: 10,
    borderRadius: 5,
  },
  reviewDetail: {
    flexDirection: 'row',
    alignContent: 'center',
    paddingBottom: 13,
    marginTop: 10,
  },
  avatarProduct: {
    width: 80,
    height: 80,
    borderRadius: 20,
    marginRight: 10,
  },
  imageDetail: {
    borderWidth: 5,
    borderRadius: 50,
    width: 50,
    height: 50,
    marginRight: 10,
    borderColor: '#F8F8FF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  avatarDetail: {
    width: 40,
    height: 40,
    borderRadius: 50,
  },
  tag: {
    ...TypoSkin.title4,
    fontWeight: '700',
    color: '#555',
  },
  size: {
    fontSize: 7,
    color: '#888',
  },
  orderDetail: {
    flexDirection: 'row',
    margin: 10,
  },
  orderName: {
    ...TypoSkin.body2,
    color: 'blue',
    marginLeft: 5,
    width: '100%',
  },
  scrollContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 20,
  },
});

export default ReviewProductIteCard;
