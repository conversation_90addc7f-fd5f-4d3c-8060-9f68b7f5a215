/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  TouchableOpacity,
  View,
  Image,
  Text,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { RootScreen } from '../../router/router';
import { shopStyles } from './styles';
const NotShop = () => {
  const navigation = useNavigation<any>();
  const handleRegisterShop = () => {
    navigation.navigate(RootScreen.RegisterShop);
  };
  return (
    <View style={shopStyles.containetNotShop}>
      <Image
        source={require('../../assets/images/registerShop.png')}
        style={shopStyles.illustrationNotShop}
      />
      <Text style={shopStyles.descriptionNotShop}>
        Bạn là khách hàng cá nhân, Chọn{'\n'}
        <Text style={shopStyles.boldTextNotShop}>“Đ<PERSON><PERSON> ký”</Text> để chuyển đổi thành cửa
        hàng
      </Text>
      <TouchableOpacity
        style={shopStyles.buyButtonNotShop}
        onPress={() => handleRegisterShop()}>
        <Text style={shopStyles.actionButtonTextNotShop}>Đăng Ký</Text>
      </TouchableOpacity>
    </View>
  );
};
export default NotShop;
