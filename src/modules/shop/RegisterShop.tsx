/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import {
  View,
} from 'react-native';
import { useRoute } from '@react-navigation/native';
import { Title } from '../../Config/Contanst';
import { LeaderShopInfoData } from '../../components/dto/dto';
import { InforHeader } from '../../Screen/Layout/headers/inforHeader';
import StoreInfoForm from './form/InputShopInfo';
import { shopStyles } from './styles';
const RegisterShop = () => {
  const route = useRoute<any>();
  const [statusInput, setStatusInput] = useState<string>('');
  const [data, setData] = useState<LeaderShopInfoData>();
  useEffect(() => {
    if (route?.params?.data) {
      if (route?.params?.status && route?.params?.status == 'edit') {
        setStatusInput(route?.params?.status);
      }
      if (route?.params?.data && route?.params?.data?.name) {
        setData(route?.params?.data);
      }
    }
  }),
    [route];

  return (
    <View style={shopStyles.containerRegisterShop}>
      <InforHeader
        title={statusInput == 'edit' ? Title.Edit : Title.Register}
      />
      <StoreInfoForm
        statusInput={statusInput}
        data={data as LeaderShopInfoData}
      />
    </View>
  );
};
export default RegisterShop;
