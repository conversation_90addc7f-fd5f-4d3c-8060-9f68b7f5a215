import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Title } from '../../Config/Contanst';
import { InforHeader } from '../../Screen/Layout/headers/inforHeader';
import Chart from '../chart/Chart';

const ChartReport = () => {
  return (
    <View style={styles.container}>
      {/* Header */}
      <InforHeader title={Title.Report} />
      <Chart />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
});

export default ChartReport;
