import { useState } from "react";
import { Controller } from "react-hook-form";
import { Modal, TouchableOpacity, View } from "react-native";
import { TypoSkin } from "../../../assets/skin/typography";
import { Text } from "react-native-paper";
import { ColorThemes } from "../../../assets/skin/colors";
import { Winicon } from "wini-mobile-components";
import { FlatList } from "react-native-gesture-handler";

export const CustomDropdownNoBorder = ({
    control,
    name,
    placeholder,
    options,
    required = false,
    errors,
    style = {},
}: {
    control: any;
    name: string;
    placeholder: string;
    options: Array<{ id: number; name: string }>;
    required?: boolean;
    errors: any;
    style?: any;
}) => {
    const [isVisible, setIsVisible] = useState(false);
    const [selectedItem, setSelectedItem] = useState<any>(null);

    return (
        <Controller
            control={control}
            name={name}
            rules={{ required: required }}
            render={({ field }) => {
                // Tìm item tương ứng với giá trị hiện tại
                const currentItem = options.find(item => item.id === field.value);

                return (
                    <View style={[{ flex: 1 }, style]}>
                        <TouchableOpacity
                            onPress={() => setIsVisible(true)}
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                paddingVertical: 12,
                                paddingHorizontal: 8,
                                height: 48,
                            }}>
                            <Text
                                style={{
                                    flex: 1,
                                    ...TypoSkin.body3,
                                    fontFamily: 'Roboto',
                                    color: currentItem
                                        ? ColorThemes.light.neutral_text_primary_color
                                        : ColorThemes.light.neutral_text_subtitle_color,
                                }}>
                                {currentItem ? currentItem.name : placeholder}
                            </Text>
                            <Winicon
                                src="outline/arrows/chevron-down"
                                size={16}
                                color={ColorThemes.light.neutral_text_subtitle_color}
                            />
                        </TouchableOpacity>
                        <Modal
                            visible={isVisible}
                            transparent={true}
                            animationType="fade"
                            onRequestClose={() => setIsVisible(false)}>
                            <TouchableOpacity
                                style={{
                                    flex: 1,
                                    backgroundColor: 'rgba(0,0,0,0.5)',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}
                                activeOpacity={1}
                                onPress={() => setIsVisible(false)}>
                                <View
                                    style={{
                                        backgroundColor: 'white',
                                        borderRadius: 8,
                                        width: '80%',
                                        maxHeight: '60%',
                                    }}>
                                    <FlatList
                                        data={options}
                                        keyExtractor={item => item.id.toString()}
                                        renderItem={({ item }) => (
                                            <TouchableOpacity
                                                style={{
                                                    paddingVertical: 15,
                                                    paddingHorizontal: 20,
                                                    borderBottomWidth: 0.5,
                                                    borderBottomColor: '#eee',
                                                }}
                                                onPress={() => {
                                                    setSelectedItem(item);
                                                    field.onChange(item.id);
                                                    setIsVisible(false);
                                                }}>
                                                <Text
                                                    style={{
                                                        fontSize: 16,
                                                        fontFamily: 'Roboto',
                                                        color:
                                                            field.value === item.id
                                                                ? ColorThemes.light.primary_main_color
                                                                : ColorThemes.light.neutral_text_primary_color,
                                                    }}>
                                                    {item.name}
                                                </Text>
                                            </TouchableOpacity>
                                        )}
                                    />
                                </View>
                            </TouchableOpacity>
                        </Modal>

                        {errors[name] && (
                            <Text style={{ color: 'red', fontSize: 12, marginTop: 4, fontFamily: 'Roboto' }}>
                                {errors[name].message ||
                                    `Vui lòng chọn ${placeholder.toLowerCase()}`}
                            </Text>
                        )}
                    </View>
                );
            }}
        />
    );
};