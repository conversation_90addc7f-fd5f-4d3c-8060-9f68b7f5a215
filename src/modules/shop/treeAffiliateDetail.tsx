import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from 'react-native';
import { FBottomSheet, showBottomSheet, Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import { useNavigation, useRoute } from '@react-navigation/native';

import HeaderShop from './component/HeaderShop';
import {
  NumberStatusIcon,
  StatusOrder,
  TransactionStatus,
  TransactionType,
} from '../../Config/Contanst';
import LinearGradient from 'react-native-linear-gradient';

// Import thêm các dependencies cần thiết
import { DataController } from '../../base/baseController';
import store from '../../redux/store/store';
import { LoadingUI } from '../../features/loading';
import { RootScreen } from '../../router/router';
import ConfigAPI from '../../Config/ConfigAPI';
import { Ultis } from '../../utils/Utils';
import { InforHeader } from '../../Screen/Layout/headers/inforHeader';
import WalletDA from '../wallet/da';
import SearchAffiliate from './searchAffiliate';

const TreeAffiliateDetail = () => {
  const [data, setData] = useState<any>();
  const [loading, setLoading] = useState(false);
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const Customercontroller = new DataController('Customer');
  const Ordercontroller = new DataController('Order');
  const rewardHistorycontroller = new DataController('HistoryReward');
  const [members, setMembers] = useState<any>([]);
  // Lấy customerId từ params hoặc sử dụng customer hiện tại
  const customerId = route.params?.customerId;
  const username = route.params?.username;
  const customerBottomSheetRef = useRef<any>(null);
  const fetchData = async () => {
    setLoading(true);

    const customer = store.getState().customer.data;
    const targetCustomerId = customerId || customer.Id;
    const walletDA = new WalletDA();
    // Chỉ lấy con cấp ngay dưới của customerId
    const [customerRes, rewardRes] = await Promise.all([
      Customercontroller.getListSimple({
        query: `@ListParent: (*${targetCustomerId}*)`,
        returns: ['Id', 'Name', 'ParentId', 'ListParent', 'AvatarUrl'],
        sortby: { BY: 'DateCreated', DIRECTION: 'ASC' },
      }),
      rewardHistorycontroller.group({
        reducers:
          'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
        searchRaw: `@CustomerId: {${targetCustomerId}} @Type: [${TransactionType.hoahong}] @Status: [${TransactionStatus.success}]`,
      }),
    ]);
    if (customerRes.code === 200 && rewardRes.code === 200) {
      //lấy hết danh sách member in tree
      var members = [];
      const memberRes = await Customercontroller.getListSimple({
        query: `@ListParent: (*${targetCustomerId}*)`,
        returns: ['Id', 'Name', 'ParentId', 'ListParent', 'AvatarUrl', 'Mobile'],
        sortby: { BY: 'DateCreated', DIRECTION: 'ASC' },
      });
      if (memberRes.code === 200 && memberRes.data.length > 0) {
        members = memberRes.data.map((item: any) => item.Id);
        setMembers(memberRes.data);
      }
      // var customerIds = customerRes.data.map((item: any) => item.Id);
      // if (customerIds.length === 0) {
      //   setLoading(false);
      //   return;
      // }
      const customerIds = [targetCustomerId, ...members];
      const orderRes = await Ordercontroller.group({
        reducers:
          'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalPrice',
        searchRaw: `@CustomerId: {${customerIds?.join(' | ')}} @Status: [${StatusOrder.success
          }]`,
      });

      var totalprice = orderRes.data.reduce(
        (total: number, item: any) => total + parseFloat(item.TotalPrice),
        0,
      );
      var totalreward = rewardRes.data.reduce(
        (total: number, item: any) => total + parseFloat(item.TotalReward),
        0,
      );

      // Đơn giản hóa: chỉ tạo danh sách con cấp ngay dưới
      const affiliateList = customerRes.data
        .filter((item: any) => item.ParentId === targetCustomerId)
        .map((item: any) => {
          const revenue =
            orderRes.data
              ?.filter((a: any) => a.CustomerId === item.Id)
              .reduce(
                (total: number, order: any) => total + order.TotalPrice,
                0,
              ) || 0;
          return {
            id: item.Id,
            name: item.Name,
            avatar: item.AvatarUrl,
            level: 'F1', // Tất cả đều là F1 vì là con cấp ngay dưới
            levelLabel: 'F1',
            levelColor: ColorThemes.light.primary_main_color,
            members: customerRes.data.filter((a: any) => a.ParentId === item.Id)
              .length,
            revenue: revenue,
            hasChildren:
              customerRes.data.filter((a: any) => a.ParentId === item.Id)
                .length > 0,
          };
        });

      setData({
        userInfo: {
          name: customer.Name,
          stats: {
            totalMembers: customerIds.length,
            groupRevenue: Ultis.money(totalprice),
            commission: Ultis.money(totalreward),
          },
        },
        affiliateList: affiliateList,
      });
    }

    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [customerId, username]);
  const handleItemClick = (item: any) => {
    navigation.push(RootScreen.TreeAffiliateDetail, {
      customerId: item.id,
      username: item.name,
    });
  };
  // Component thông tin user và thống kê
  const UserStatsSection = () => (
    <View style={styles.userStatsContainer}>
      <Text style={styles.userName}>{username}</Text>

      <View style={styles.statsRow}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Tổng thành viên</Text>
          <Text
            style={[
              styles.statValue,
              { color: ColorThemes.light.primary_main_color },
            ]}>
            {data?.userInfo?.stats?.totalMembers || 0}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Doanh thu nhóm</Text>
          <Text
            style={[
              styles.statValue,
              { color: ColorThemes.light.secondary1_main_color },
            ]}>
            {data?.userInfo?.stats?.groupRevenue || 0}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Hoa hồng</Text>
          <Text
            style={[
              styles.statValue,
              { color: ColorThemes.light.secondary3_main_color },
            ]}>
            {data?.userInfo?.stats?.commission || 0}
          </Text>
        </View>
      </View>
    </View>
  );

  // Component item affiliate
  const AffiliateItem = ({ item }: { item: any }) => (
    <LinearGradient
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
      style={{ borderRadius: 16 }}>
      <TouchableOpacity
        style={styles.affiliateItem}
        onPress={() => handleItemClick(item)}>
        <View style={styles.affiliateLeft}>
          <View style={[styles.levelBadge, { backgroundColor: item.levelColor }]}>
            <Text style={styles.levelText}>{item.levelLabel}</Text>
          </View>

          <View style={styles.affiliateInfo}>
            <View style={styles.nameRow}>
              <Image
                source={{
                  uri: item.avatar
                    ? `${ConfigAPI.urlImg + item.avatar}`
                    : 'https://placehold.co/48/FFFFFF/000000/png',
                }}
                style={{ width: 16, height: 16, borderRadius: 50 }}
              />
              <Text style={styles.affiliateName}>{item.name}</Text>
            </View>

            <View style={styles.detailsRow}>
              <View style={styles.detailItem}>
                <Winicon
                  src="outline/files/file-money"
                  size={14}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
                <Text style={styles.detailText}>
                  {Ultis.money(item.revenue)}
                </Text>
              </View>

              <View style={styles.detailItem}>
                <Winicon
                  src="outline/sport/users-mm"
                  size={14}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
                <Text style={styles.detailText}>{item.members}</Text>
              </View>
            </View>
          </View>
        </View>

        {item.hasChildren && (
          <TouchableOpacity style={styles.expandButton}>
            <Winicon
              src="fill/layout/plus"
              size={20}
              color={ColorThemes.light.primary_main_color}
            />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    </LinearGradient>
  );

  return (
    <View style={styles.container}>
      {/* Background gradient */}
      <FBottomSheet ref={customerBottomSheetRef} />
      <InforHeader title="Cây sơ đồ"
        showAction={true}
        customActions={
          <TouchableOpacity style={styles.searchButton}
            onPress={() => {
              // navigation.navigate(RootScreen.SearchAffiliate, {
              //   members: members,
              // });
              showBottomSheet({
                ref: customerBottomSheetRef,
                enableDismiss: true,
                title: 'Tìm kiếm thành viên',
                children: (
                  <SearchAffiliate
                    members={members}
                    customerBottomSheetRef={customerBottomSheetRef}
                  />
                ),
              });
            }}
          >
            <Winicon
              src="outline/user interface/search"
              size={20}
              color={ColorThemes.light.neutral_text_title_color}
            />
          </TouchableOpacity>
        }
      />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>

        <UserStatsSection />
        {loading ? (
          <LoadingUI isLoading={loading} />
        ) : (
          <View style={styles.affiliateList}>
            {data?.affiliateList && data.affiliateList.length > 0 ? (
              data.affiliateList.map((item: any) => (
                <AffiliateItem key={item.id} item={item} />
              ))
            ) : (
              <View style={styles.emptyContainer}>
                <Winicon
                  src=""
                  size={48}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
                <Text style={styles.emptyText}>Không có cấp con</Text>
                <Text style={styles.emptySubText}>
                  Chưa có thành viên nào trong cấp này
                </Text>
              </View>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 200,
    backgroundColor: ColorThemes.light.primary_main_color,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
  },
  backButton: {
    padding: 8,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  logoContainer: {
    alignItems: 'center',
  },
  logo: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoText: {
    fontSize: 16,
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: '600',
  },
  searchButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  userStatsContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingHorizontal: 16,
    paddingVertical: 20,
    marginBottom: 16,
  },
  userName: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'left',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statLabel: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 8,
    textAlign: 'center',
  },
  statValue: {
    ...TypoSkin.heading5,
    fontWeight: '700',
  },
  affiliateList: {
    paddingHorizontal: 16,
    gap: 12,
  },
  affiliateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  affiliateLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  levelBadge: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  levelText: {
    ...TypoSkin.medium1,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: '600',
  },
  affiliateInfo: {
    flex: 1,
    gap: 8,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  affiliateName: {
    ...TypoSkin.medium1,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '500',
  },
  detailsRow: {
    flexDirection: 'row',
    gap: 16,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  detailText: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  expandButton: {
    padding: 8,
  },
  // Empty State Styles
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 12,
    marginHorizontal: 16,
  },
  emptyText: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubText: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default TreeAffiliateDetail;
