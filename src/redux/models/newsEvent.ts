interface NewsEvent {
  DateCreated: number;
  Id: string;
  Key: string;
  DateEnd: number;
  DateStart: number;
  Code: string;
  Title: string;
  Address: string;
  Img: string;
  Description: string;
  Name: string;
  Content: string;
  Sort: number;
  Status: number;
  Type: number;
  isRegistered?: boolean;
  Views?: number;
  EventLink?: string;
  AddressLink?: string;
  Hashtag: string;
  LikesCount?: number;
  Comment?: number;
  Likes?: Array<any>;
  IsLike?: boolean;
}

export type {NewsEvent};
