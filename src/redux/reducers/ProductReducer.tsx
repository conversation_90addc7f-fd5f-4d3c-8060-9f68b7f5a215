import {useNavigation} from '@react-navigation/native';
import {
  Dispatch,
  PayloadAction,
  UnknownAction,
  createSlice,
} from '@reduxjs/toolkit';
import ProductDA from '../../modules/Product/da';
import {useSelectorShopState} from '../hook/shopHook ';
import {Product} from '../models/product';
import {fetchProducts, updateFavoriteProduct} from '../actions/productAction';
import {updateArrayWithObjects} from '../../utils/arrayUtils';

interface ProductSimpleResponse {
  data: Product[];
  totalCount: number;
  onLoading?: boolean;
  type?: string;
}

const initState: ProductSimpleResponse = {
  data: [],
  totalCount: 0,
  onLoading: false,
};

export const productSlice = createSlice({
  name: 'Product',
  initialState: initState,
  reducers: {
    handleActionsProduct: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETINFORPRODUCT':
          state.data = action.payload.data;
          break;
        case 'UPDATE':
          state.data = action.payload.data;
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    setData: <K extends keyof ProductSimpleResponse>(
      state: ProductSimpleResponse,
      action: PayloadAction<{
        stateName: K;
        data: ProductSimpleResponse[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchProducts.pending, state => {
      state.onLoading = true;
    });
    builder.addCase(
      fetchProducts.fulfilled,
      (state: ProductSimpleResponse, action: any) => {
        if (action.payload.isLoadMore) {
          state.data = [...state.data, ...action.payload.data];
        } else {
          state.data = action.payload.data;
        }
        state.totalCount = action.payload.totalCount;
        state.onLoading = false;
      },
    );
    builder.addCase(
      updateFavoriteProduct.fulfilled,
      (state: ProductSimpleResponse, action: any) => {
        state.data = updateArrayWithObjects(state.data, [action.payload]);
        state.onLoading = false;
      },
    );
  },
});

export const {handleActionsProduct} = productSlice.actions;

export default productSlice.reducer;

export class ProductActions {
  static getInforProduct = (shopId: string) => async (dispatch: Dispatch) => {
    const productDA = new ProductDA();
    let data = [
      {
        name: 'Còn hàng',
        number: 0,
        data: [],
      },
      {
        name: 'Hết hàng',
        number: 0,
        data: [],
      },
      {
        name: 'Chờ duyệt',
        number: 0,
        data: [],
      },
      {
        name: 'Vi phạm',
        number: 0,
        data: [],
      },
      {
        name: 'Ẩn',
        number: 0,
        data: [],
      },
    ];
    console.log('652541819819519--------------------------------------');

    // Gọi 1 API duy nhất để lấy tất cả sản phẩm
    let allProductsResponse = await productDA.getAllProducts(shopId);
    console.log('allProductsResponse', allProductsResponse);

    if (allProductsResponse?.length > 0) {
      const allProducts = allProductsResponse;
      console.log('allProducts', allProducts);

      // Lọc và phân loại sản phẩm theo status
      data[0].data = allProducts.filter(
        (product: Product) => product.Status === 1,
      ); // Còn hàng
      data[0].number = data[0].data.length;
      data[0].data = data[0].data;

      data[1].data = allProducts.filter(
        (product: Product) => product.Status === 2,
      ); // Hết hàng
      data[1].number = data[1].data.length;
      data[1].data = data[1].data;

      data[2].data = allProducts.filter(
        (product: Product) => product.Status === 3,
      ); // Chờ duyệt
      data[2].number = data[2].data.length;
      data[2].data = data[2].data;

      data[3].data = allProducts.filter(
        (product: Product) => product.Status === 4,
      ); // Vi phạm
      data[3].number = data[3].data.length;
      data[3].data = data[3].data;

      data[4].data = allProducts.filter(
        (product: Product) => product.Status === 5,
      ); // Ẩn
      data[4].number = data[4].data.length;
      data[4].data = data[4].data;
    }

    dispatch(
      handleActionsProduct({
        type: 'GETINFORPRODUCT',
        data: data,
      }),
    );
  };
}
