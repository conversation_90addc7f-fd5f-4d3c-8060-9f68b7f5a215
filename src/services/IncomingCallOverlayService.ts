// Service để quản lý incoming call notification state
// Sử dụng simple callback pattern thay vì EventEmitter

interface CallData {
  callerName: string;
  callerAvatar?: string;
  callerId: string;
}

type EventCallback = (data?: any) => void;

class IncomingCallOverlayService {
  private static instance: IncomingCallOverlayService;
  private isShowing = false;
  private currentCallData: CallData | null = null;
  private listeners: { [event: string]: EventCallback[] } = {};

  private constructor() {}

  static getInstance(): IncomingCallOverlayService {
    if (!IncomingCallOverlayService.instance) {
      IncomingCallOverlayService.instance = new IncomingCallOverlayService();
    }
    return IncomingCallOverlayService.instance;
  }

  // Add event listener
  on(event: string, callback: EventCallback): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
    console.log(`📞 Added listener for ${event}, total: ${this.listeners[event].length}`);
  }

  // Remove event listener
  off(event: string, callback: EventCallback): void {
    if (!this.listeners[event]) return;

    const index = this.listeners[event].indexOf(callback);
    if (index > -1) {
      this.listeners[event].splice(index, 1);
      console.log(`📞 Removed listener for ${event}, remaining: ${this.listeners[event].length}`);
    }
  }

  // Emit event
  emit(event: string, data?: any): void {
    console.log(`📞 Emitting event ${event} with data:`, data);
    if (!this.listeners[event]) {
      console.log(`📞 No listeners for event ${event}`);
      return;
    }

    this.listeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`📞 Error in listener for ${event}:`, error);
      }
    });
  }

  // Get listener count
  listenerCount(event: string): number {
    return this.listeners[event] ? this.listeners[event].length : 0;
  }

  // Hiển thị incoming call notification
  showIncomingCall(callData: CallData): void {
    console.log('📞 Showing incoming call notification for:', callData.callerName);

    // Reset state trước khi show notification mới
    if (this.isShowing) {
      console.log('📞 Notification already showing, hiding first');
      this.hideIncomingCall();

      // Delay một chút để đảm bảo state được reset
      setTimeout(() => {
        this.showIncomingCall(callData);
      }, 200);
      return;
    }

    this.isShowing = true;
    this.currentCallData = callData;

    // Emit event để UI components có thể listen và hiển thị
    this.emit('showIncomingCall', callData);
  }

  // Ẩn incoming call notification
  hideIncomingCall(): void {
    console.log('📞 hideIncomingCall called, current isShowing:', this.isShowing);

    if (!this.isShowing) {
      console.log('📞 Notification not showing, nothing to hide');
      return;
    }

    this.isShowing = false;
    this.currentCallData = null;
    console.log('📞 Hiding incoming call notification, state reset');

    // Emit event để UI components ẩn notification
    this.emit('hideIncomingCall');
  }

  // Accept cuộc gọi
  acceptCall(): void {
    if (this.currentCallData) {
      console.log('📞 Accepting call from:', this.currentCallData.callerName);
      debugger;
      this.emit('acceptCall', this.currentCallData);
      this.hideIncomingCall();
    }
  }

  // Reject cuộc gọi
  rejectCall(): void {
    if (this.currentCallData) {
      console.log('📞 Rejecting call from:', this.currentCallData.callerName);
      this.emit('rejectCall', this.currentCallData);
      this.hideIncomingCall();
    }
  }

  // Kiểm tra xem có đang hiển thị notification không
  isShowingNotification(): boolean {
    return this.isShowing;
  }

  // Lấy thông tin cuộc gọi hiện tại
  getCurrentCallData(): CallData | null {
    return this.currentCallData;
  }

  // Force reset state (để debug)
  forceReset(): void {
    console.log('📞 Force resetting IncomingCallOverlayService state');
    this.isShowing = false;
    this.currentCallData = null;
    this.emit('hideIncomingCall');
  }

  // Get current state (để debug)
  getState(): { isShowing: boolean; currentCallData: CallData | null } {
    return {
      isShowing: this.isShowing,
      currentCallData: this.currentCallData,
    };
  }
}

export default IncomingCallOverlayService.getInstance();
