/**
 * Utility để debug socket connections
 * Giúp theo dõi số lượng kết nối socket và phát hiện duplicate connections
 */

class SocketDebugger {
  private static connections: Map<string, { timestamp: number; userId: string; name: string }> = new Map();
  
  /**
   * Log khi có socket connection mới
   */
  static logConnection(socketId: string, userId: string, name: string) {
    const timestamp = Date.now();
    this.connections.set(socketId, { timestamp, userId, name });
    
    console.log(`🔌 [SOCKET DEBUG] New connection:`, {
      socketId,
      userId,
      name,
      timestamp: new Date(timestamp).toISOString(),
      totalConnections: this.connections.size
    });
    
    // Cảnh báo nếu có nhiều hơn 1 kết nối
    if (this.connections.size > 1) {
      console.warn(`⚠️ [SOCKET DEBUG] Multiple connections detected! Total: ${this.connections.size}`);
      this.logAllConnections();
    }
  }
  
  /**
   * <PERSON>g khi socket disconnect
   */
  static logDisconnection(socketId: string) {
    const connection = this.connections.get(socketId);
    if (connection) {
      this.connections.delete(socketId);
      console.log(`🔌 [SOCKET DEBUG] Connection closed:`, {
        socketId,
        userId: connection.userId,
        duration: Date.now() - connection.timestamp,
        remainingConnections: this.connections.size
      });
    }
  }
  
  /**
   * Log tất cả connections hiện tại
   */
  static logAllConnections() {
    console.log(`🔌 [SOCKET DEBUG] All active connections (${this.connections.size}):`);
    this.connections.forEach((connection, socketId) => {
      console.log(`  - ${socketId}: ${connection.userId} (${connection.name}) - ${new Date(connection.timestamp).toISOString()}`);
    });
  }
  
  /**
   * Kiểm tra có duplicate connections không
   */
  static checkDuplicateConnections(): boolean {
    return this.connections.size > 1;
  }
  
  /**
   * Clear tất cả tracking data
   */
  static clear() {
    this.connections.clear();
    console.log(`🔌 [SOCKET DEBUG] Cleared all connection tracking data`);
  }
  
  /**
   * Get số lượng connections hiện tại
   */
  static getConnectionCount(): number {
    return this.connections.size;
  }
}

export default SocketDebugger;
